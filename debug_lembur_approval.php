<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Lembur;

// Ambil Lembur ID 5
$lembur = Lembur::with(['approvalStatus', 'approvals'])->find(5);

if (!$lembur) {
    echo "Lembur ID 5 tidak ditemukan\n";
    exit;
}

echo "=== DEBUG LEMBUR APPROVAL STATUS ===\n\n";

echo "Lembur ID: {$lembur->id}\n";
echo "Tanggal: {$lembur->tanggal}\n";
echo "Jumlah Jam: {$lembur->jumlah_jam}\n";
echo "Karyawan ID: {$lembur->karyawan_id}\n\n";

// Cek approval status
if ($lembur->approvalStatus) {
    echo "=== APPROVAL STATUS ===\n";
    echo "Status: {$lembur->approvalStatus->status}\n";
    echo "Creator ID: {$lembur->approvalStatus->creator_id}\n";
    echo "Created At: {$lembur->approvalStatus->created_at}\n";
    echo "Updated At: {$lembur->approvalStatus->updated_at}\n\n";

    // Cek method dari ApprovableModel
    echo "=== APPROVABLE MODEL METHODS ===\n";
    echo "isSubmitted(): " . ($lembur->isSubmitted() ? 'true' : 'false') . "\n";
    echo "isApprovalCompleted(): " . ($lembur->isApprovalCompleted() ? 'true' : 'false') . "\n";

    try {
        echo "isRejected(): " . ($lembur->isRejected() ? 'true' : 'false') . "\n";
    } catch (Exception $e) {
        echo "isRejected(): method not available\n";
    }

    try {
        echo "isDiscarded(): " . ($lembur->isDiscarded() ? 'true' : 'false') . "\n";
    } catch (Exception $e) {
        echo "isDiscarded(): method not available\n";
    }
    echo "\n";

    // Cek approval records
    if ($lembur->approvals) {
        echo "=== APPROVAL RECORDS ===\n";
        foreach ($lembur->approvals as $approval) {
            echo "Approval ID: {$approval->id}\n";
            echo "Action: {$approval->approval_action}\n";
            echo "User ID: {$approval->user_id}\n";
            echo "Comment: {$approval->comment}\n";
            echo "Created At: {$approval->created_at}\n";
            echo "---\n";
        }
    } else {
        echo "Tidak ada approval records\n";
    }
} else {
    echo "Tidak ada approval status\n";
}

// Test filter logic yang digunakan di PayrollService
echo "\n=== TEST FILTER LOGIC ===\n";

$shouldInclude = false;

if (!$lembur->approvalStatus) {
    $shouldInclude = true;
    echo "Logic: Tidak ada approval status -> Include (legacy data)\n";
} else {
    $isCompleted = $lembur->isApprovalCompleted();
    $isApproved = $lembur->approvalStatus->status === 'Approved';

    echo "isApprovalCompleted(): " . ($isCompleted ? 'true' : 'false') . "\n";
    echo "status === 'Approved': " . ($isApproved ? 'true' : 'false') . "\n";

    $shouldInclude = $isCompleted && $isApproved;
    echo "Logic: isApprovalCompleted() && status === 'Approved' -> " . ($shouldInclude ? 'Include' : 'Exclude') . "\n";
}

echo "Final Decision: " . ($shouldInclude ? 'INCLUDE in payroll' : 'EXCLUDE from payroll') . "\n";

// Cek apakah ada masalah dengan status
if ($lembur->approvalStatus && $lembur->isApprovalCompleted() && $lembur->approvalStatus->status === 'Pending') {
    echo "\n=== MASALAH DITEMUKAN ===\n";
    echo "Status 'Pending' tapi isApprovalCompleted() = true\n";
    echo "Ini menunjukkan inkonsistensi dalam sistem approval\n";
    echo "Kemungkinan penyebab:\n";
    echo "1. Approval flow sudah selesai tapi status belum diupdate\n";
    echo "2. Ada bug dalam sistem approval\n";
    echo "3. Data approval tidak sinkron\n";
}
