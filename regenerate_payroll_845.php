<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\PayrollTransaction;
use App\Services\PayrollService;
use Illuminate\Support\Facades\DB;

// Ambil PayrollTransaction ID 845
$payrollTransaction = PayrollTransaction::with(['karyawan', 'payrollPeriod'])->find(845);

if (!$payrollTransaction) {
    echo "PayrollTransaction ID 845 tidak ditemukan\n";
    exit;
}

echo "=== REGENERATE PAYROLL TRANSACTION ID 845 ===\n\n";

echo "Karyawan: {$payrollTransaction->karyawan->nama_lengkap} (ID: {$payrollTransaction->karyawan_id})\n";
echo "Periode: {$payrollTransaction->payrollPeriod->nama_periode}\n";
echo "Status: {$payrollTransaction->status}\n\n";

// Cek apakah bisa di-regenerate (hanya jika status draft)
if ($payrollTransaction->status !== 'draft') {
    echo "❌ TIDAK BISA REGENERATE: Status payroll bukan 'draft' (status: {$payrollTransaction->status})\n";
    echo "Hanya payroll dengan status 'draft' yang bisa di-regenerate\n";
    exit;
}

echo "=== NILAI LEMBUR SEBELUM REGENERATE ===\n";
echo "Lembur Biasa: Rp " . number_format($payrollTransaction->lembur_biasa, 0, ',', '.') . "\n";
echo "Lembur Tanggal Merah: Rp " . number_format($payrollTransaction->lembur_tanggal_merah, 0, ',', '.') . "\n";
echo "Lembur Tambah HK: Rp " . number_format($payrollTransaction->lembur_tambah_hk, 0, ',', '.') . "\n";
echo "Total Take Home Pay: Rp " . number_format($payrollTransaction->take_home_pay, 0, ',', '.') . "\n\n";

try {
    DB::beginTransaction();
    
    echo "🔄 Memulai regenerate payroll...\n";
    
    // Hapus payroll transaction yang lama
    $oldId = $payrollTransaction->id;
    $karyawan = $payrollTransaction->karyawan;
    $period = $payrollTransaction->payrollPeriod;
    
    $payrollTransaction->delete();
    echo "✅ Payroll transaction lama dihapus\n";
    
    // Generate ulang menggunakan PayrollService
    $payrollService = new PayrollService();
    $payrollService->generatePayrollForKaryawan($period, $karyawan);
    echo "✅ Payroll transaction baru dibuat\n";
    
    // Ambil payroll transaction yang baru
    $newPayrollTransaction = PayrollTransaction::where('karyawan_id', $karyawan->id)
        ->where('payroll_period_id', $period->id)
        ->latest()
        ->first();
    
    if ($newPayrollTransaction) {
        echo "\n=== NILAI LEMBUR SETELAH REGENERATE ===\n";
        echo "New Payroll Transaction ID: {$newPayrollTransaction->id}\n";
        echo "Lembur Biasa: Rp " . number_format($newPayrollTransaction->lembur_biasa, 0, ',', '.') . "\n";
        echo "Lembur Tanggal Merah: Rp " . number_format($newPayrollTransaction->lembur_tanggal_merah, 0, ',', '.') . "\n";
        echo "Lembur Tambah HK: Rp " . number_format($newPayrollTransaction->lembur_tambah_hk, 0, ',', '.') . "\n";
        echo "Total Take Home Pay: Rp " . number_format($newPayrollTransaction->take_home_pay, 0, ',', '.') . "\n\n";
        
        // Bandingkan nilai lembur
        $totalLemburOld = $payrollTransaction->lembur_biasa + $payrollTransaction->lembur_tanggal_merah + $payrollTransaction->lembur_tambah_hk;
        $totalLemburNew = $newPayrollTransaction->lembur_biasa + $newPayrollTransaction->lembur_tanggal_merah + $newPayrollTransaction->lembur_tambah_hk;
        
        echo "=== PERBANDINGAN ===\n";
        echo "Total Lembur Lama: Rp " . number_format($totalLemburOld, 0, ',', '.') . "\n";
        echo "Total Lembur Baru: Rp " . number_format($totalLemburNew, 0, ',', '.') . "\n";
        echo "Selisih: Rp " . number_format($totalLemburNew - $totalLemburOld, 0, ',', '.') . "\n\n";
        
        if ($totalLemburNew > $totalLemburOld) {
            echo "✅ BERHASIL: Komponen lembur sekarang sudah termasuk lembur yang approved!\n";
            echo "URL baru: http://viera.test/admin/payroll-transactions/{$newPayrollTransaction->id}\n";
        } else if ($totalLemburNew == $totalLemburOld) {
            echo "ℹ️  INFO: Nilai lembur sama, tidak ada perubahan\n";
        } else {
            echo "⚠️  WARNING: Nilai lembur baru lebih kecil dari sebelumnya\n";
        }
    } else {
        echo "❌ ERROR: Payroll transaction baru tidak ditemukan\n";
        DB::rollback();
        exit;
    }
    
    DB::commit();
    echo "\n🎉 Regenerate payroll berhasil!\n";
    
} catch (Exception $e) {
    DB::rollback();
    echo "❌ ERROR: {$e->getMessage()}\n";
    echo "Regenerate payroll gagal, perubahan di-rollback\n";
}
