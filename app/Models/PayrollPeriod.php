<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PayrollPeriod extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'kode_periode',
        'nama_periode',
        'tanggal_mulai',
        'tanggal_selesai',
        'tanggal_cutoff',
        'status',
        'keterangan',
        'karyawan_ids',
        'created_by',
        'processed_by',
        'processed_at',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'tanggal_cutoff' => 'date',
        'processed_at' => 'datetime',
        'karyawan_ids' => 'array',
    ];

    /**
     * Relasi ke PayrollTransaction
     */
    public function payrollTransactions()
    {
        return $this->hasMany(PayrollTransaction::class);
    }

    /**
     * <PERSON>lasi ke User yang membuat
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relasi ke User yang memproses
     */
    public function processor()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Relasi ke Karyawan yang dipilih untuk periode ini
     */
    public function selectedKaryawan()
    {
        if (!$this->karyawan_ids) {
            return collect();
        }

        return \App\Models\Karyawan::whereIn('id', $this->karyawan_ids)
            ->where('status_aktif', true)
            ->with(['jabatan', 'departemen', 'entitas'])
            ->get();
    }

    /**
     * Boot method untuk auto-generate kode periode
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->kode_periode) {
                $model->kode_periode = $model->generateKodePeriode();
            }
        });
    }

    /**
     * Generate kode periode otomatis
     */
    private function generateKodePeriode()
    {
        $prefix = 'PAY';
        $year = Carbon::parse($this->tanggal_mulai)->format('Y');
        $month = Carbon::parse($this->tanggal_mulai)->format('m');

        $latestId = static::max('id') + 1;

        return $prefix . '-' . $year . $month . '-' . str_pad($latestId, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Scope untuk periode aktif
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['draft', 'processing', 'completed']);
    }

    /**
     * Scope berdasarkan status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Cek apakah periode bisa diproses
     */
    public function canBeProcessed()
    {
        return $this->status === 'draft' && !empty($this->karyawan_ids) && count($this->karyawan_ids) > 0;
    }

    /**
     * Cek apakah periode sudah selesai
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Mulai pemrosesan periode
     */
    public function startProcessing($userId)
    {
        $this->update([
            'status' => 'processing',
            'processed_by' => $userId,
            'processed_at' => now(),
        ]);
    }

    /**
     * Selesaikan pemrosesan periode
     */
    public function completeProcessing()
    {
        $this->update([
            'status' => 'completed',
        ]);
    }

    /**
     * Batalkan periode
     */
    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
        ]);
    }

    /**
     * Accessor untuk badge status
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'draft' => 'secondary',
            'processing' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    /**
     * Accessor untuk format periode
     */
    public function getFormattedPeriodeAttribute()
    {
        return $this->tanggal_mulai->format('d M Y') . ' - ' . $this->tanggal_selesai->format('d M Y');
    }

    /**
     * Get total karyawan dalam periode ini
     */
    public function getTotalKaryawanAttribute()
    {
        return $this->payrollTransactions()->distinct('karyawan_id')->count();
    }

    /**
     * Get total payroll amount
     */
    public function getTotalPayrollAttribute()
    {
        return $this->payrollTransactions()->sum('take_home_pay');
    }

    /**
     * Get jumlah karyawan yang dipilih untuk periode ini
     */
    public function getJumlahKaryawanTerpilihAttribute()
    {
        return $this->karyawan_ids ? count($this->karyawan_ids) : 0;
    }
}
