<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PayrollTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'no_payroll',
        'payroll_period_id',
        'karyawan_id',
        'penggajian_karyawan_id',
        'gaji_pokok',
        'tunjangan_jabatan',
        'tunjangan_umum',
        'tunjangan_sembako',
        'lembur_biasa',
        'lembur_tanggal_merah',
        'lembur_tambah_hk',
        'kekurangan_gaji_bulan_sebelum',
        'claim_sakit_dengan_surat',
        'pesangon',
        'insentif',
        'total_gaji_kotor',
        'potongan_bpjs_kesehatan',
        'potongan_bpjs_tk',
        'potongan_keterlambatan',
        'potongan_pelanggaran',
        'potongan_lainnya',
        'total_potongan',
        'take_home_pay',
        'total_hari_kerja',
        'total_hari_hadir',
        'total_menit_terlambat',
        'total_pelanggaran',
        'sakit_dengan_surat',
        'sakit_tanpa_surat',
        'izin',
        'ambil_cuti',
        'sisa_cuti',
        'status',
        'keterangan',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'gaji_pokok' => 'decimal:2',
        'tunjangan_jabatan' => 'decimal:2',
        'tunjangan_umum' => 'decimal:2',
        'tunjangan_sembako' => 'decimal:2',
        'total_gaji_kotor' => 'decimal:2',
        'potongan_bpjs_kesehatan' => 'decimal:2',
        'potongan_bpjs_tk' => 'decimal:2',
        'potongan_keterlambatan' => 'decimal:2',
        'potongan_pelanggaran' => 'decimal:2',
        'potongan_lainnya' => 'decimal:2',
        'total_potongan' => 'decimal:2',
        'take_home_pay' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    /**
     * Relasi ke PayrollPeriod
     */
    public function payrollPeriod()
    {
        return $this->belongsTo(PayrollPeriod::class);
    }

    /**
     * Relasi ke Karyawan
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Relasi ke PenggajianKaryawan (basis gaji)
     */
    public function penggajianKaryawan()
    {
        return $this->belongsTo(PenggajianKaryawan::class);
    }

    /**
     * Relasi ke PayrollDeduction
     */
    public function payrollDeductions()
    {
        return $this->hasMany(PayrollDeduction::class);
    }

    /**
     * Relasi ke Absensi berdasarkan periode dan karyawan
     */
    public function absensiRecords()
    {
        return $this->hasMany(\App\Models\Absensi::class, 'karyawan_id', 'karyawan_id')
            ->whereHas('jadwal', function ($query) {
                $query->whereBetween('tanggal_jadwal', [
                    $this->payrollPeriod->tanggal_mulai ?? now()->startOfMonth(),
                    $this->payrollPeriod->tanggal_cutoff ?? now()->endOfMonth()
                ]);
            })
            ->with(['jadwal.shift', 'jadwal.entitas', 'karyawan']);
    }

    /**
     * Relasi ke Absensi yang terlambat dalam periode
     */
    public function lateAbsensiRecords()
    {
        return $this->absensiRecords()
            ->where('status', 'terlambat')
            ->where('is_tolerance_given', false);
    }

    /**
     * Relasi ke User yang membuat
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relasi ke User yang approve
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Boot method untuk auto-calculate dan generate nomor
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->no_payroll) {
                $model->no_payroll = $model->generateNoPayroll();
            }

            $model->calculateTotals();
        });

        static::updating(function ($model) {
            $model->calculateTotals();
        });
    }

    /**
     * Generate nomor payroll otomatis
     */
    private function generateNoPayroll()
    {
        $prefix = 'PAY';
        $latestId = static::max('id') + 1;

        return $prefix . '-' . str_pad($latestId, 8, '0', STR_PAD_LEFT);
    }

    /**
     * Hitung total-total otomatis
     */
    private function calculateTotals()
    {
        // Hitung total gaji kotor (termasuk komponen lembur dan penerimaan lainnya)
        $this->total_gaji_kotor = $this->gaji_pokok + $this->tunjangan_jabatan +
            $this->tunjangan_umum + $this->tunjangan_sembako +
            $this->lembur_biasa + $this->lembur_tanggal_merah + $this->lembur_tambah_hk +
            $this->kekurangan_gaji_bulan_sebelum + $this->claim_sakit_dengan_surat +
            $this->pesangon + $this->insentif;

        // Hitung total potongan
        $this->total_potongan = $this->potongan_bpjs_kesehatan + $this->potongan_bpjs_tk +
            $this->potongan_keterlambatan + $this->potongan_pelanggaran +
            $this->potongan_lainnya;

        // Hitung take home pay
        $this->take_home_pay = max(0, $this->total_gaji_kotor - $this->total_potongan);
    }

    /**
     * Scope berdasarkan status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope untuk payroll yang sudah diapprove
     */
    public function scopeApproved($query)
    {
        return $query->whereNotNull('approved_at');
    }

    /**
     * Approve payroll
     */
    public function approve($userId)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $userId,
            'approved_at' => now(),
        ]);

        // Buat record di riwayat gaji setelah approve
        $this->createRiwayatGaji();

        // Tandai kekurangan gaji dan pesangon sebagai sudah dibayar
        $this->markKekuranganGajiAsPaid();
        $this->markPesangonAsPaid();
    }

    /**
     * Buat record di riwayat gaji setelah payroll diapprove
     */
    private function createRiwayatGaji()
    {
        // Cek apakah sudah ada record untuk periode ini
        $existingRecord = \App\Models\PenggajianKaryawan::where('karyawan_id', $this->karyawan_id)
            ->where('periode_gaji', $this->payrollPeriod->nama_periode)
            ->first();

        if (!$existingRecord) {
            \App\Models\PenggajianKaryawan::create([
                'karyawan_id' => $this->karyawan_id,
                'no_penggajian' => $this->no_payroll,
                'periode_gaji' => $this->payrollPeriod->nama_periode,
                'gaji_pokok' => $this->gaji_pokok,
                'tunjangan_jabatan' => $this->tunjangan_jabatan,
                'tunjangan_umum' => $this->tunjangan_umum,
                'tunjangan_sembako' => $this->tunjangan_sembako,
                'bpjs_kesehatan_dipotong' => $this->potongan_bpjs_kesehatan,
                'bpjs_tk_dipotong' => $this->potongan_bpjs_tk,
                'potongan_lainnya' => $this->potongan_keterlambatan + $this->potongan_pelanggaran + $this->potongan_lainnya,
                'take_home_pay' => $this->take_home_pay,
                'keterangan' => "Hasil payroll periode {$this->payrollPeriod->nama_periode} - Approved pada " . now()->format('d M Y H:i'),
            ]);
        }
    }

    /**
     * Mark as paid
     */
    public function markAsPaid()
    {
        $this->update([
            'status' => 'paid',
        ]);
    }

    /**
     * Cancel payroll
     */
    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
        ]);
    }

    /**
     * Accessor untuk badge status
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'draft' => 'secondary',
            'approved' => 'success',
            'paid' => 'primary',
            'cancelled' => 'danger',
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    /**
     * Accessor untuk format take home pay
     */
    public function getFormattedTakeHomePayAttribute()
    {
        return 'Rp ' . number_format($this->take_home_pay, 0, ',', '.');
    }

    /**
     * Tandai kekurangan gaji sebagai sudah dibayar
     */
    private function markKekuranganGajiAsPaid()
    {
        if ($this->kekurangan_gaji_bulan_sebelum > 0) {
            \App\Models\KekuranganGaji::where('karyawan_id', $this->karyawan_id)
                ->where('status', 'approved')
                ->where('periode_kekurangan', '<', $this->payrollPeriod->tanggal_mulai)
                ->whereNull('tanggal_pembayaran')
                ->update([
                    'status' => 'paid',
                    'tanggal_pembayaran' => now(),
                ]);
        }
    }

    /**
     * Tandai pesangon sebagai sudah dibayar
     */
    private function markPesangonAsPaid()
    {
        if ($this->pesangon > 0) {
            \App\Models\Pesangon::where('karyawan_id', $this->karyawan_id)
                ->where('status', 'approved')
                ->where('periode_pembayaran', '<=', $this->payrollPeriod->tanggal_mulai->format('Y-m-01'))
                ->whereNull('tanggal_pembayaran')
                ->update([
                    'status' => 'paid',
                    'tanggal_pembayaran' => now(),
                ]);
        }
    }
}
