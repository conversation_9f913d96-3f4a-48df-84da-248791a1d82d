<?php

namespace App\Filament\Resources\PayrollPeriodResource\Pages;

use App\Filament\Resources\PayrollPeriodResource;
use App\Models\Karyawan;
use App\Services\PayrollService;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class ProcessPayrollPeriod extends EditRecord
{
    protected static string $resource = PayrollPeriodResource::class;

    protected static string $view = 'filament.resources.payroll-period-resource.pages.process-payroll-period';

    public ?array $data = [];

    public function mount(int | string $record): void
    {
        parent::mount($record);

        // Cek apakah periode bisa diproses
        if (!$this->record->canBeProcessed()) {
            Notification::make()
                ->title('Periode tidak dapat diproses')
                ->body('Periode payroll ini sudah diproses atau dibatalkan.')
                ->danger()
                ->send();

            $this->redirect(PayrollPeriodResource::getUrl('index'));
            return;
        }

        // Cek apakah ada karyawan yang dipilih
        if (empty($this->record->karyawan_ids)) {
            Notification::make()
                ->title('Belum ada karyawan dipilih')
                ->body('Pilih karyawan terlebih dahulu di form edit periode.')
                ->warning()
                ->send();

            $this->redirect(PayrollPeriodResource::getUrl('edit', ['record' => $record]));
            return;
        }

        $this->form->fill([
            'karyawan_ids' => $this->record->karyawan_ids,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Karyawan yang Akan Diproses')
                    ->description('Daftar karyawan yang telah dipilih untuk periode payroll ini.')
                    ->schema([
                        CheckboxList::make('karyawan_ids')
                            ->label('Karyawan')
                            ->options(function () {
                                // Hanya tampilkan karyawan yang sudah dipilih di periode
                                if (empty($this->record->karyawan_ids)) {
                                    return [];
                                }

                                return Karyawan::whereIn('id', $this->record->karyawan_ids)
                                    ->where('status_aktif', true)
                                    ->whereHas('penggajian')
                                    ->with(['jabatan', 'departemen', 'entitas', 'penggajian' => function ($q) {
                                        $q->latest();
                                    }])
                                    ->get()
                                    ->mapWithKeys(function ($karyawan) {
                                        $gaji = $karyawan->penggajian->first();
                                        $gajiInfo = $gaji ? 'Rp ' . number_format($gaji->gaji_pokok, 0, ',', '.') : 'Belum ada gaji';
                                        $entitasInfo = $karyawan->entitas ? " - {$karyawan->entitas->nama_entitas}" : '';

                                        return [
                                            $karyawan->id => "{$karyawan->nama_lengkap} - {$karyawan->jabatan?->nama_jabatan}{$entitasInfo} ({$gajiInfo})"
                                        ];
                                    })
                                    ->toArray();
                            })
                            ->columns(2)
                            ->gridDirection('row')
                            ->required()
                            ->disabled() // Tidak bisa diubah di halaman proses
                            ->helperText('Karyawan ini telah dipilih di pengaturan periode. Untuk mengubah, kembali ke halaman edit periode.'),
                    ]),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('process')
                ->label('Proses Payroll')
                ->icon('heroicon-o-play')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Konfirmasi Proses Payroll')
                ->modalDescription('Apakah Anda yakin ingin memproses payroll untuk periode ini? Proses ini akan menghitung semua komponen gaji, potongan keterlambatan, dan pelanggaran.')
                ->modalSubmitActionLabel('Ya, Proses Payroll')
                ->action('processPayroll'),

            Actions\Action::make('back')
                ->label('Kembali')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(fn() => PayrollPeriodResource::getUrl('index')),
        ];
    }

    public function processPayroll(): void
    {
        // Gunakan karyawan yang sudah dipilih di periode, bukan dari form
        $karyawanIds = $this->record->karyawan_ids;

        if (empty($karyawanIds)) {
            Notification::make()
                ->title('Tidak ada karyawan dipilih')
                ->body('Pilih karyawan terlebih dahulu di pengaturan periode.')
                ->warning()
                ->send();
            return;
        }

        try {
            DB::beginTransaction();

            // Update status periode menjadi processing
            $this->record->startProcessing(auth()->id());

            // Proses payroll menggunakan service dengan karyawan yang sudah dipilih
            $payrollService = new PayrollService();
            $payrollService->generatePayroll($this->record, $karyawanIds);

            // Update status periode menjadi completed
            $this->record->completeProcessing();

            DB::commit();

            Notification::make()
                ->title('Payroll berhasil diproses')
                ->body('Payroll untuk ' . count($karyawanIds) . ' karyawan telah berhasil diproses.')
                ->success()
                ->send();

            $this->redirect(PayrollPeriodResource::getUrl('view', ['record' => $this->record]));
        } catch (\Exception $e) {
            DB::rollback();

            Notification::make()
                ->title('Gagal memproses payroll')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function getTitle(): string
    {
        return 'Proses Payroll - ' . $this->record->nama_periode;
    }
}
