<?php

namespace App\Filament\Resources\PayrollTransactionResource\Pages;

use App\Filament\Resources\PayrollTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditPayrollTransaction extends EditRecord
{
    protected static string $resource = PayrollTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn() => $this->record->status === 'draft'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    public function mount(int | string $record): void
    {
        parent::mount($record);

        // Cek apakah payroll bisa diedit
        if ($this->record->status !== 'draft') {
            Notification::make()
                ->title('Tidak Dapat Mengedit')
                ->body('Payroll yang sudah diapprove tidak dapat diedit.')
                ->danger()
                ->send();

            $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
            return;
        }
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Hitung ulang total gaji kotor
        $totalGajiKotor = ($data['gaji_pokok'] ?? 0) +
                         ($data['tunjangan_jabatan'] ?? 0) +
                         ($data['tunjangan_umum'] ?? 0) +
                         ($data['tunjangan_sembako'] ?? 0) +
                         ($data['lembur_biasa'] ?? 0) +
                         ($data['lembur_tanggal_merah'] ?? 0) +
                         ($data['lembur_tambah_hk'] ?? 0) +
                         ($data['kekurangan_gaji_bulan_sebelum'] ?? 0) +
                         ($data['claim_sakit_dengan_surat'] ?? 0) +
                         ($data['pesangon'] ?? 0) +
                         ($data['insentif'] ?? 0);

        $data['total_gaji_kotor'] = $totalGajiKotor;

        // Hitung ulang total potongan
        $totalPotongan = ($data['potongan_bpjs_kesehatan'] ?? 0) +
                        ($data['potongan_bpjs_tk'] ?? 0) +
                        ($data['potongan_keterlambatan'] ?? 0) +
                        ($data['potongan_pelanggaran'] ?? 0) +
                        ($data['potongan_lainnya'] ?? 0);

        $data['total_potongan'] = $totalPotongan;

        // Hitung ulang take home pay
        $data['take_home_pay'] = $totalGajiKotor - $totalPotongan;

        return $data;
    }

    protected function afterSave(): void
    {
        Notification::make()
            ->title('Berhasil Disimpan')
            ->body('Data payroll berhasil diupdate.')
            ->success()
            ->send();
    }
}
