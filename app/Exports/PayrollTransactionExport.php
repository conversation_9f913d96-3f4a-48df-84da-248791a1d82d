<?php

namespace App\Exports;

use App\Models\PayrollTransaction;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PayrollTransactionExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths, WithStyles
{
    protected $periodId;

    public function __construct($periodId = null)
    {
        $this->periodId = $periodId;
    }

    public function collection()
    {
        $query = PayrollTransaction::with([
            'karyawan.jabatan',
            'payrollPeriod',
            'payrollDeductions'
        ]);

        if ($this->periodId) {
            $query->where('payroll_period_id', $this->periodId);
        }

        return $query->orderBy('karyawan_id')->get();
    }

    public function headings(): array
    {
        return [
            'NO',
            'NRK',
            'Nama',
            'TMK',
            'HKE',
            'HK yang Dibayar',
            'SAKIT + SURAT',
            'SAKIT - SURAT',
            'TANPA KET / IZIN',
            'TERLAMBAT',
            'AMBIL CUTI',
            'SISA CUTI',
            'GAJI POKOK',
            'TUNJANGAN JABATAN',
            'TUNJANGAN UMUM',
            'TUNJANGAN SEMBAKO',
            'LEMBUR BIASA',
            'MASUK TANGGAL MERAH',
            'JML LEMBUR HK',
            'LEMBUR TAMBAH HKE',
            'POTONGAN MINUS KASIR',
            'POTONGAN STOCK OPNAME',
            'POTONGAN RETUR',
            'POTONGAN KASBON',
            'TAKE HOME PAY (Gross)',
            'KEKURANGAN GAJI BULAN LALU',
            'POTONGAN MAKAN',
            'CHECK BOX',
            'Hitungan rahasia',
            'Tot. Hari tanpa hitungan',
            'Proporsional',
            'Tot. Gaji Proporsional',
            'KPI 1'
        ];
    }

    public function map($transaction): array
    {
        static $counter = 0;
        $counter++;

        // Get deduction amounts by type
        $deductions = $this->getDeductionsByType($transaction);

        return [
            $counter, // NO
            $transaction->karyawan->nip ?? '-', // NRK
            $transaction->karyawan->nama_lengkap ?? '-', // Nama
            0, // TMK (Tanggal masuk kerja)
            $transaction->total_hari_kerja ?? 0, // HKE (Hari Kerja Efektif)
            $transaction->total_hari_hadir ?? 0, // HK yang Dibayar
            $transaction->sakit_dengan_surat ?? 0, // SAKIT + SURAT
            $transaction->sakit_tanpa_surat ?? 0, // SAKIT - SURAT
            $transaction->izin ?? 0, // TANPA KET / IZIN
            $this->calculateLatenessDays($transaction), // TERLAMBAT (hari)
            $transaction->ambil_cuti ?? 0, // AMBIL CUTI
            $transaction->sisa_cuti ?? 0, // SISA CUTI
            $this->formatCurrency($transaction->gaji_pokok), // GAJI POKOK
            $this->formatCurrency($transaction->tunjangan_jabatan), // TUNJANGAN JABATAN
            $this->formatCurrency($transaction->tunjangan_umum), // TUNJANGAN UMUM
            $this->formatCurrency($transaction->tunjangan_sembako), // TUNJANGAN SEMBAKO
            $this->formatCurrency($transaction->lembur_biasa), // LEMBUR BIASA
            $this->formatCurrency($transaction->lembur_tanggal_merah), // MASUK TANGGAL MERAH
            $this->formatCurrency($transaction->lembur_tambah_hk), // JML LEMBUR HK
            $this->formatCurrency($transaction->lembur_tambah_hk), // LEMBUR TAMBAH HKE (sama dengan JML LEMBUR HK)
            $this->formatCurrency($deductions['kasir']), // POTONGAN MINUS KASIR
            $this->formatCurrency($deductions['stok_opname']), // POTONGAN STOCK OPNAME
            $this->formatCurrency($deductions['retur']), // POTONGAN RETUR
            $this->formatCurrency($deductions['kasbon']), // POTONGAN KASBON
            $this->formatCurrency($transaction->take_home_pay), // TAKE HOME PAY (Gross)
            $this->formatCurrency($transaction->kekurangan_gaji_bulan_sebelum), // KEKURANGAN GAJI BULAN LALU
            $this->formatCurrency($deductions['makan']), // POTONGAN MAKAN
            '', // CHECK BOX (kosong untuk diisi manual)
            '', // Hitungan rahasia (kosong)
            $this->calculateTotalDaysWithoutCalculation($transaction), // Tot. Hari tanpa hitungan
            $this->calculateProportional($transaction), // Proporsional
            $this->formatCurrency($this->calculateProportionalSalary($transaction)), // Tot. Gaji Proporsional
            '' // KPI 1 (kosong untuk diisi manual)
        ];
    }

    /**
     * Get deductions by type from PayrollDeduction records
     */
    private function getDeductionsByType($transaction): array
    {
        $deductions = [
            'kasir' => 0,
            'stok_opname' => 0,
            'retur' => 0,
            'kasbon' => 0,
            'makan' => 0,
        ];

        foreach ($transaction->payrollDeductions as $deduction) {
            switch ($deduction->jenis_potongan) {
                case 'kasir':
                    $deductions['kasir'] += $deduction->nominal;
                    break;
                case 'stok_opname':
                    $deductions['stok_opname'] += $deduction->nominal;
                    break;
                case 'retur':
                    $deductions['retur'] += $deduction->nominal;
                    break;
                case 'kasbon':
                    $deductions['kasbon'] += $deduction->nominal;
                    break;
                case 'makan':
                case 'potongan_makan':
                    $deductions['makan'] += $deduction->nominal;
                    break;
            }
        }

        return $deductions;
    }

    /**
     * Calculate lateness in days (convert minutes to days)
     */
    private function calculateLatenessDays($transaction): int
    {
        $totalMinutes = $transaction->total_menit_terlambat ?? 0;
        // Assuming 8 hours work day = 480 minutes
        return $totalMinutes > 0 ? ceil($totalMinutes / 480) : 0;
    }

    /**
     * Calculate total days without calculation
     */
    private function calculateTotalDaysWithoutCalculation($transaction): int
    {
        $totalDays = $transaction->total_hari_kerja ?? 0;
        $paidDays = $transaction->total_hari_hadir ?? 0;
        $sickWithLetter = $transaction->sakit_dengan_surat ?? 0;
        $leave = $transaction->ambil_cuti ?? 0;

        return $totalDays - $paidDays - $sickWithLetter - $leave;
    }

    /**
     * Calculate proportional percentage
     */
    private function calculateProportional($transaction): string
    {
        $totalDays = $transaction->total_hari_kerja ?? 0;
        $paidDays = $transaction->total_hari_hadir ?? 0;

        if ($totalDays == 0) return '0%';

        $percentage = ($paidDays / $totalDays) * 100;
        return number_format($percentage, 1) . '%';
    }

    /**
     * Calculate proportional salary
     */
    private function calculateProportionalSalary($transaction): float
    {
        $totalDays = $transaction->total_hari_kerja ?? 0;
        $paidDays = $transaction->total_hari_hadir ?? 0;
        $baseSalary = $transaction->gaji_pokok ?? 0;

        if ($totalDays == 0) return 0;

        return ($paidDays / $totalDays) * $baseSalary;
    }

    /**
     * Format currency without currency symbol
     */
    private function formatCurrency($amount): string
    {
        return number_format($amount ?? 0, 0, ',', '.');
    }

    /**
     * Set column widths
     */
    public function columnWidths(): array
    {
        return [
            'A' => 5,   // NO
            'B' => 12,  // NRK
            'C' => 25,  // Nama
            'D' => 8,   // TMK
            'E' => 8,   // HKE
            'F' => 12,  // HK yang Dibayar
            'G' => 12,  // SAKIT + SURAT
            'H' => 12,  // SAKIT - SURAT
            'I' => 15,  // TANPA KET / IZIN
            'J' => 10,  // TERLAMBAT
            'K' => 12,  // AMBIL CUTI
            'L' => 10,  // SISA CUTI
            'M' => 15,  // GAJI POKOK
            'N' => 15,  // TUNJANGAN JABATAN
            'O' => 15,  // TUNJANGAN UMUM
            'P' => 15,  // TUNJANGAN SEMBAKO
            'Q' => 15,  // LEMBUR BIASA
            'R' => 18,  // MASUK TANGGAL MERAH
            'S' => 15,  // JML LEMBUR HK
            'T' => 18,  // LEMBUR TAMBAH HKE
            'U' => 18,  // POTONGAN MINUS KASIR
            'V' => 20,  // POTONGAN STOCK OPNAME
            'W' => 15,  // POTONGAN RETUR
            'X' => 15,  // POTONGAN KASBON
            'Y' => 18,  // TAKE HOME PAY (Gross)
            'Z' => 22,  // KEKURANGAN GAJI BULAN LALU
            'AA' => 15, // POTONGAN MAKAN
            'AB' => 12, // CHECK BOX
            'AC' => 15, // Hitungan rahasia
            'AD' => 20, // Tot. Hari tanpa hitungan
            'AE' => 12, // Proporsional
            'AF' => 20, // Tot. Gaji Proporsional
            'AG' => 8,  // KPI 1
        ];
    }

    /**
     * Apply styles to the worksheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Header row styling
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 10,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE2E2E2',
                    ],
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    'wrapText' => true,
                ],
            ],
            // Data rows styling
            'A2:AG1000' => [
                'font' => [
                    'size' => 9,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ],
            // Number columns alignment (right align for currency)
            'M2:AG1000' => [
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT,
                ],
            ],
        ];
    }
}
