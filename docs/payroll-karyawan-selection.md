# Fitur Pemilihan Karyawan pada Periode Payroll

## Overview
Fitur ini memungkinkan admin untuk memilih karyawan yang akan diproses payrollnya langsung pada saat membuat atau mengedit periode payroll, bukan pada saat proses payroll. Hal ini memberikan kontrol yang lebih baik dan menghindari duplikasi pemilihan karyawan.

## Fitur Utama

### 1. Pemilihan Karyawan di Form Periode Payroll
- Karyawan dapat dipilih langsung saat membuat periode payroll baru
- Karyawan dapat diubah saat mengedit periode payroll (hanya untuk status draft)
- <PERSON>ya karyawan aktif dengan data penggajian yang ditampilkan
- Informasi yang ditampilkan: Nama, Jabatan, Entitas, Gaji Pokok

### 2. Validasi Proses Payroll
- Periode payroll hanya bisa diproses jika sudah ada karyawan yang dipilih
- Validasi dilakukan di level model dan UI
- Pesan error yang jelas jika belum ada karyawan dipilih

### 3. Action Kelola Karyawan
- Action terpisah untuk mengelola karyawan pada periode yang sudah ada
- Form modal dengan checkbox list untuk memudahkan pemilihan
- Bulk toggle untuk select all/none

### 4. Tampilan Informasi Karyawan
- Kolom "Karyawan Terpilih" di tabel periode payroll
- Badge indicator untuk status pemilihan karyawan
- Informasi jumlah karyawan yang dipilih

## Perubahan Database

### Migration: `add_karyawan_ids_to_payroll_periods_table`
```sql
ALTER TABLE payroll_periods ADD COLUMN karyawan_ids JSON NULL COMMENT 'ID karyawan yang dipilih untuk periode ini';
```

### Model Changes
- Tambah field `karyawan_ids` ke fillable
- Cast `karyawan_ids` sebagai array
- Method `selectedKaryawan()` untuk mendapatkan data karyawan
- Method `canBeProcessed()` diupdate untuk validasi karyawan
- Accessor `jumlah_karyawan_terpilih` untuk UI

## Perubahan UI

### Form Periode Payroll
- Section "Pemilihan Karyawan" dengan CheckboxList
- Searchable dan bulk toggleable
- Disabled untuk periode non-draft
- Helper text yang informatif

### Tabel Periode Payroll
- Kolom "Karyawan Terpilih" dengan badge
- Action "Kelola Karyawan" untuk edit cepat
- Action "Proses Payroll" dengan konfirmasi

### Halaman Process Payroll
- Menampilkan karyawan yang sudah dipilih (read-only)
- Redirect ke edit jika belum ada karyawan dipilih
- Menggunakan karyawan dari periode, bukan form

## Backward Compatibility

### Seeder untuk Data Existing
Seeder `UpdatePayrollPeriodKaryawanSeeder` akan:
- Mengupdate periode payroll yang belum memiliki karyawan_ids
- Mengisi dengan semua karyawan aktif yang memiliki penggajian
- Memberikan log progress untuk monitoring

### Migration Strategy
1. Jalankan migration untuk menambah kolom
2. Jalankan seeder untuk mengupdate data existing
3. Test fitur pada periode baru dan existing

## Testing

### Unit Tests
File: `tests/Feature/PayrollPeriodKaryawanSelectionTest.php`

Test cases:
- ✅ Periode payroll dapat menyimpan karyawan yang dipilih
- ✅ Periode payroll dapat mengambil data karyawan yang dipilih
- ✅ Periode payroll hanya bisa diproses jika ada karyawan dipilih
- ✅ Accessor jumlah_karyawan_terpilih berfungsi dengan benar
- ✅ Pemilihan karyawan dapat diupdate

## Cara Penggunaan

### 1. Membuat Periode Payroll Baru
1. Buka menu Payroll > Periode Payroll
2. Klik "Tambah Periode Payroll"
3. Isi informasi periode (nama, tanggal, dll)
4. Di section "Pemilihan Karyawan", pilih karyawan yang akan diproses
5. Simpan periode

### 2. Mengelola Karyawan pada Periode Existing
1. Di tabel periode payroll, klik action "Kelola Karyawan"
2. Update pilihan karyawan sesuai kebutuhan
3. Simpan perubahan

### 3. Memproses Payroll
1. Pastikan karyawan sudah dipilih (badge "X karyawan")
2. Klik action "Proses Payroll"
3. Konfirmasi proses dengan informasi jumlah karyawan
4. Sistem akan memproses payroll untuk karyawan yang dipilih

## Benefits

1. **Efisiensi**: Tidak perlu memilih karyawan berulang kali
2. **Konsistensi**: Karyawan yang sama untuk seluruh periode
3. **Kontrol**: Admin dapat mengatur karyawan per periode
4. **Transparansi**: Jelas karyawan mana yang akan diproses
5. **Validasi**: Mencegah proses payroll tanpa karyawan

## Troubleshooting

### Periode tidak bisa diproses
- Pastikan status periode adalah "draft"
- Pastikan sudah ada karyawan yang dipilih
- Cek apakah karyawan masih aktif dan memiliki data penggajian

### Karyawan tidak muncul di pilihan
- Pastikan karyawan status_aktif = true
- Pastikan karyawan memiliki data di tabel penggajian_karyawan
- Refresh halaman jika baru saja menambah data karyawan

### Error saat proses payroll
- Cek log aplikasi untuk detail error
- Pastikan semua karyawan yang dipilih masih valid
- Cek koneksi database dan integritas data
