# Perbaikan Payroll: Hanya Mengambil Data yang Sudah Di-Approve

## Overview
Implementasi perbaikan pada sistem payroll untuk memastikan bahwa:
1. **Lembur**: Hanya mengambil lembur yang sudah di-approve melalui sistem approval
2. **Kekurangan Gaji**: Hanya mengambil kekurangan gaji dengan status 'approved' dan belum dibayar
3. **Pesangon**: Hanya mengambil pesangon dengan status 'approved' dan belum dibayar
4. **Auto-mark as Paid**: Otomatis menandai kekurangan gaji dan pesangon sebagai 'paid' setelah payroll di-approve

## Perubahan yang Dilakukan

### 1. Perbaikan Filter Lembur (PayrollService.php)

**Sebelum:**
```php
$lemburRecords = \App\Models\Lembur::where('karyawan_id', $karyawan->id)
    ->whereBetween('tanggal', [$period->tanggal_mulai, $period->tanggal_cutoff])
    ->with('jenisLembur')
    ->get();
```

**Sesudah:**
```php
// Ambil semua lembur dalam periode, kemudian filter yang sudah approved
$allLemburRecords = \App\Models\Lembur::where('karyawan_id', $karyawan->id)
    ->whereBetween('tanggal', [$period->tanggal_mulai, $period->tanggal_cutoff])
    ->with(['jenisLembur', 'approvalStatus'])
    ->get();

// Filter hanya lembur yang sudah di-approve
$lemburRecords = $allLemburRecords->filter(function ($lembur) {
    // Jika tidak ada approval status, anggap sebagai data lama yang valid
    if (!$lembur->approvalStatus) {
        return true;
    }
    
    // Gunakan method isApprovalCompleted() dari ApprovableModel
    return $lembur->isApprovalCompleted() && 
           $lembur->approvalStatus->status === 'Approved';
});
```

**Penjelasan:**
- Menggunakan `isApprovalCompleted()` method dari `ApprovableModel`
- Backward compatible dengan data lama yang belum ada approval system
- Hanya mengambil lembur dengan status 'Approved'

### 2. Perbaikan Query Kekurangan Gaji

**Sebelum:**
```php
$kekuranganGaji = \App\Models\KekuranganGaji::where('karyawan_id', $karyawan->id)
    ->where('status', 'approved')
    ->where('periode_kekurangan', '<', $period->tanggal_mulai)
    ->sum('nominal_kekurangan');
```

**Sesudah:**
```php
$kekuranganGaji = \App\Models\KekuranganGaji::byKaryawan($karyawan->id)
    ->byStatus('approved')
    ->where('periode_kekurangan', '<', $period->tanggal_mulai)
    ->whereNull('tanggal_pembayaran') // Belum dibayar
    ->sum('nominal_kekurangan');
```

**Penjelasan:**
- Menggunakan scope methods yang sudah ada di model
- Menambahkan filter `whereNull('tanggal_pembayaran')` untuk memastikan belum dibayar
- Hanya mengambil kekurangan gaji dari periode sebelumnya

### 3. Perbaikan Query Pesangon

**Sebelum:**
```php
$pesangon = \App\Models\Pesangon::where('karyawan_id', $karyawan->id)
    ->where('status', 'approved')
    ->where('periode_pembayaran', $period->tanggal_mulai->format('Y-m-01'))
    ->sum('nominal_pesangon');
```

**Sesudah:**
```php
$pesangon = \App\Models\Pesangon::byKaryawan($karyawan->id)
    ->byStatus('approved')
    ->where('periode_pembayaran', '<=', $period->tanggal_mulai->format('Y-m-01'))
    ->whereNull('tanggal_pembayaran') // Belum dibayar
    ->sum('nominal_pesangon');
```

**Penjelasan:**
- Menggunakan scope methods yang sudah ada di model
- Menambahkan filter `whereNull('tanggal_pembayaran')` untuk memastikan belum dibayar
- Mengambil pesangon untuk periode ini dan sebelumnya (`<=`)

### 4. Auto-mark as Paid (PayrollTransaction.php)

**Ditambahkan method baru:**
```php
/**
 * Approve payroll
 */
public function approve($userId)
{
    $this->update([
        'status' => 'approved',
        'approved_by' => $userId,
        'approved_at' => now(),
    ]);

    // Buat record di riwayat gaji setelah approve
    $this->createRiwayatGaji();

    // Tandai kekurangan gaji dan pesangon sebagai sudah dibayar
    $this->markKekuranganGajiAsPaid();
    $this->markPesangonAsPaid();
}

/**
 * Tandai kekurangan gaji sebagai sudah dibayar
 */
private function markKekuranganGajiAsPaid()
{
    if ($this->kekurangan_gaji_bulan_sebelum > 0) {
        \App\Models\KekuranganGaji::where('karyawan_id', $this->karyawan_id)
            ->where('status', 'approved')
            ->where('periode_kekurangan', '<', $this->payrollPeriod->tanggal_mulai)
            ->whereNull('tanggal_pembayaran')
            ->update([
                'status' => 'paid',
                'tanggal_pembayaran' => now(),
            ]);
    }
}

/**
 * Tandai pesangon sebagai sudah dibayar
 */
private function markPesangonAsPaid()
{
    if ($this->pesangon > 0) {
        \App\Models\Pesangon::where('karyawan_id', $this->karyawan_id)
            ->where('status', 'approved')
            ->where('periode_pembayaran', '<=', $this->payrollPeriod->tanggal_mulai->format('Y-m-01'))
            ->whereNull('tanggal_pembayaran')
            ->update([
                'status' => 'paid',
                'tanggal_pembayaran' => now(),
            ]);
    }
}
```

**Penjelasan:**
- Otomatis menandai kekurangan gaji dan pesangon sebagai 'paid' ketika payroll di-approve
- Mengupdate `tanggal_pembayaran` dengan timestamp saat ini
- Hanya mengupdate data yang sesuai dengan kriteria yang sama dengan query payroll

## Data Test

### Seeder untuk Test Data
File: `database/seeders/KekuranganGajiPesangonTestSeeder.php`

Seeder ini membuat:
- Kekurangan gaji dengan status 'approved' untuk 5 karyawan
- Pesangon dengan status 'approved' untuk beberapa karyawan
- Data periode bulan sebelumnya untuk testing

**Menjalankan seeder:**
```bash
php artisan db:seed --class=KekuranganGajiPesangonTestSeeder
```

## Testing

### Unit Tests
File: `tests/Feature/PayrollApprovedDataSimpleTest.php`

Test cases yang berhasil:
- ✅ `test_lembur_filter_works_correctly` - Filter lembur approval berjalan dengan benar
- ✅ `test_kekurangan_gaji_query_works` - Query kekurangan gaji berjalan tanpa error
- ✅ `test_pesangon_query_works` - Query pesangon berjalan tanpa error
- ✅ `test_payroll_service_calculate_methods_work` - Method calculation di PayrollService berjalan dengan benar

**Menjalankan test:**
```bash
php artisan test tests/Feature/PayrollApprovedDataSimpleTest.php
```

## Impact pada Excel Export

Excel export (`PayrollTransactionExport`) tidak perlu diubah karena:
- Export menggunakan data yang sudah ada di `PayrollTransaction`
- Data lembur, kekurangan gaji, dan pesangon sudah dihitung dengan benar di `PayrollService`
- Export akan otomatis menggunakan data yang sudah difilter

## Backward Compatibility

### Data Lama
- Lembur tanpa approval status tetap dianggap valid
- Kekurangan gaji dan pesangon existing tetap bisa diproses
- Tidak ada breaking changes pada data existing

### Migration Strategy
1. ✅ Update PayrollService untuk filter approval
2. ✅ Update PayrollTransaction untuk auto-mark as paid
3. ✅ Buat test data dengan seeder
4. ✅ Jalankan unit tests untuk verifikasi

## Benefits

1. **Akurasi Data**: Hanya data yang sudah di-approve yang masuk ke payroll
2. **Audit Trail**: Jelas kapan kekurangan gaji dan pesangon dibayar
3. **Prevent Double Payment**: Kekurangan gaji dan pesangon tidak akan muncul lagi di payroll berikutnya
4. **Compliance**: Mengikuti workflow approval yang sudah ada
5. **Transparency**: Status pembayaran tercatat dengan jelas

## Troubleshooting

### Lembur tidak muncul di payroll
- Pastikan lembur sudah di-submit dan di-approve melalui sistem approval
- Cek status approval di tabel lembur
- Untuk data lama, pastikan tidak ada approval status yang pending

### Kekurangan gaji tidak muncul
- Pastikan status kekurangan gaji adalah 'approved'
- Pastikan `tanggal_pembayaran` masih NULL
- Pastikan periode kekurangan sebelum periode payroll

### Pesangon tidak muncul
- Pastikan status pesangon adalah 'approved'
- Pastikan `tanggal_pembayaran` masih NULL
- Pastikan periode pembayaran <= periode payroll

### Data tidak ter-mark as paid setelah approve
- Pastikan payroll transaction di-approve melalui method `approve()`
- Cek log untuk error saat update status
- Verifikasi data kekurangan gaji dan pesangon sesuai kriteria
