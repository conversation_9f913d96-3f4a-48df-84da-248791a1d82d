<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payroll_transactions', function (Blueprint $table) {
            // Menambahkan field untuk data absensi tambahan
            $table->integer('sakit_dengan_surat')->default(0)->comment('Jumlah hari sakit dengan surat dokter')->after('total_pelanggaran');
            $table->integer('sakit_tanpa_surat')->default(0)->comment('Jumlah hari sakit tanpa surat dokter')->after('sakit_dengan_surat');
            $table->integer('izin')->default(0)->comment('Jumlah hari izin')->after('sakit_tanpa_surat');
            $table->integer('ambil_cuti')->default(0)->comment('Jumlah hari cuti yang diambil')->after('izin');
            $table->integer('sisa_cuti')->default(0)->comment('Sisa kuota cuti tahunan')->after('ambil_cuti');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payroll_transactions', function (Blueprint $table) {
            $table->dropColumn([
                'sakit_dengan_surat',
                'sakit_tanpa_surat',
                'izin',
                'ambil_cuti',
                'sisa_cuti'
            ]);
        });
    }
};
