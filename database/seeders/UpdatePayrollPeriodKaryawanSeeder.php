<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PayrollPeriod;
use App\Models\Karyawan;

class UpdatePayrollPeriodKaryawanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ambil semua periode payroll yang belum memiliki karyawan_ids
        $periods = PayrollPeriod::whereNull('karyawan_ids')
            ->orWhere('karyawan_ids', '[]')
            ->orWhere('karyawan_ids', 'null')
            ->get();

        // Ambil semua karyawan aktif yang memiliki penggajian
        $defaultKaryawanIds = Karyawan::where('status_aktif', true)
            ->whereHas('penggajian')
            ->pluck('id')
            ->toArray();

        foreach ($periods as $period) {
            $period->update([
                'karyawan_ids' => $defaultKaryawanIds
            ]);
            
            $this->command->info("Updated periode {$period->nama_periode} with " . count($defaultKaryawanIds) . " karyawan");
        }

        $this->command->info("Seeder completed. Updated " . $periods->count() . " payroll periods.");
    }
}
