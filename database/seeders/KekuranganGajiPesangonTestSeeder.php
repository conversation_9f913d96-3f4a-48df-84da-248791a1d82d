<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\KekuranganGaji;
use App\Models\Pesangon;
use App\Models\Karyawan;
use App\Models\User;
use Carbon\Carbon;

class KekuranganGajiPesangonTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ambil beberapa karyawan untuk test
        $karyawans = Karyawan::where('status_aktif', true)
            ->whereHas('penggajian')
            ->limit(5)
            ->get();

        $admin = User::where('role', 'admin')->first();
        if (!$admin) {
            $admin = User::first();
        }

        foreach ($karyawans as $karyawan) {
            // Buat kekurangan gaji yang sudah approved
            KekuranganGaji::updateOrCreate(
                [
                    'karyawan_id' => $karyawan->id,
                    'periode_kekurangan' => Carbon::now()->subMonth()->startOfMonth(),
                ],
                [
                    'nominal_kekurangan' => rand(100000, 500000),
                    'keterangan' => 'Kekurangan gaji bulan sebelumnya - test data',
                    'status' => 'approved',
                    'tanggal_pembayaran' => null,
                    'created_by' => $admin->id,
                    'approved_by' => $admin->id,
                    'approved_at' => now(),
                ]
            );

            // Buat pesangon yang sudah approved (hanya untuk beberapa karyawan)
            if ($karyawan->id % 2 == 0) {
                Pesangon::updateOrCreate(
                    [
                        'karyawan_id' => $karyawan->id,
                        'periode_pembayaran' => Carbon::now()->startOfMonth(),
                    ],
                    [
                        'jenis_pesangon' => 'resign',
                        'nominal_pesangon' => rand(1000000, 3000000),
                        'keterangan' => 'Pesangon resign - test data',
                        'status' => 'approved',
                        'tanggal_pembayaran' => null,
                        'created_by' => $admin->id,
                        'approved_by' => $admin->id,
                        'approved_at' => now(),
                    ]
                );
            }

            $this->command->info("Created test data for karyawan: {$karyawan->nama_lengkap}");
        }

        $this->command->info("Test data seeder completed for " . $karyawans->count() . " karyawan");
    }
}
