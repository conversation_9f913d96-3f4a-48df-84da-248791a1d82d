<?php

namespace Database\Factories;

use App\Models\PenggajianKaryawan;
use App\Models\Karyawan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PenggajianKaryawan>
 */
class PenggajianKaryawanFactory extends Factory
{
    protected $model = PenggajianKaryawan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'karyawan_id' => Karyawan::factory(),
            'no_penggajian' => 'PAY-' . $this->faker->unique()->numerify('######'),
            'periode_gaji' => $this->faker->monthName() . ' ' . $this->faker->year(),
            'gaji_pokok' => $this->faker->numberBetween(3000000, 10000000),
            'tunjangan_jabatan' => $this->faker->numberBetween(500000, 2000000),
            'tunjangan_umum' => $this->faker->numberBetween(200000, 1000000),
            'tunjangan_sembako' => $this->faker->numberBetween(100000, 500000),
            'bpjs_kesehatan_dipotong' => $this->faker->numberBetween(50000, 200000),
            'bpjs_tk_dipotong' => $this->faker->numberBetween(30000, 150000),
            'potongan_lainnya' => $this->faker->numberBetween(0, 500000),
            'take_home_pay' => function (array $attributes) {
                $totalGaji = $attributes['gaji_pokok'] + 
                           $attributes['tunjangan_jabatan'] + 
                           $attributes['tunjangan_umum'] + 
                           $attributes['tunjangan_sembako'];
                           
                $totalPotongan = $attributes['bpjs_kesehatan_dipotong'] + 
                               $attributes['bpjs_tk_dipotong'] + 
                               $attributes['potongan_lainnya'];
                               
                return $totalGaji - $totalPotongan;
            },
            'keterangan' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Set specific salary amount.
     */
    public function withSalary(int $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'gaji_pokok' => $amount,
        ]);
    }

    /**
     * Set for specific karyawan.
     */
    public function forKaryawan(int $karyawanId): static
    {
        return $this->state(fn (array $attributes) => [
            'karyawan_id' => $karyawanId,
        ]);
    }
}
