<?php

namespace Database\Factories;

use App\Models\Pesangon;
use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Pesangon>
 */
class PesangonFactory extends Factory
{
    protected $model = Pesangon::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'karyawan_id' => Karyawan::factory(),
            'periode_pembayaran' => $this->faker->dateTimeBetween('-3 months', '+1 month'),
            'jenis_pesangon' => $this->faker->randomElement(['phk', 'pensiun', 'resign', 'kontrak_habis', 'lainnya']),
            'nominal_pesangon' => $this->faker->numberBetween(1000000, 10000000),
            'keterangan' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(['pending', 'approved', 'paid']),
            'tanggal_pembayaran' => null,
            'created_by' => User::factory(),
            'approved_by' => null,
            'approved_at' => null,
        ];
    }

    /**
     * Indicate that the pesangon is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'approved_by' => User::factory(),
            'approved_at' => now(),
        ]);
    }

    /**
     * Indicate that the pesangon is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'approved_by' => User::factory(),
            'approved_at' => now(),
            'tanggal_pembayaran' => now(),
        ]);
    }

    /**
     * Set for specific karyawan.
     */
    public function forKaryawan(int $karyawanId): static
    {
        return $this->state(fn (array $attributes) => [
            'karyawan_id' => $karyawanId,
        ]);
    }

    /**
     * Set specific amount.
     */
    public function withAmount(int $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'nominal_pesangon' => $amount,
        ]);
    }

    /**
     * Set specific jenis pesangon.
     */
    public function withJenis(string $jenis): static
    {
        return $this->state(fn (array $attributes) => [
            'jenis_pesangon' => $jenis,
        ]);
    }
}
