<?php

namespace Database\Factories;

use App\Models\KekuranganGaji;
use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\KekuranganGaji>
 */
class KekuranganGajiFactory extends Factory
{
    protected $model = KekuranganGaji::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'karyawan_id' => Karyawan::factory(),
            'periode_kekurangan' => $this->faker->dateTimeBetween('-6 months', '-1 month'),
            'nominal_kekurangan' => $this->faker->numberBetween(100000, 1000000),
            'keterangan' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(['pending', 'approved', 'paid']),
            'tanggal_pembayaran' => null,
            'created_by' => User::factory(),
            'approved_by' => null,
            'approved_at' => null,
        ];
    }

    /**
     * Indicate that the kekurangan gaji is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'approved_by' => User::factory(),
            'approved_at' => now(),
        ]);
    }

    /**
     * Indicate that the kekurangan gaji is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'approved_by' => User::factory(),
            'approved_at' => now(),
            'tanggal_pembayaran' => now(),
        ]);
    }

    /**
     * Set for specific karyawan.
     */
    public function forKaryawan(int $karyawanId): static
    {
        return $this->state(fn (array $attributes) => [
            'karyawan_id' => $karyawanId,
        ]);
    }

    /**
     * Set specific amount.
     */
    public function withAmount(int $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'nominal_kekurangan' => $amount,
        ]);
    }
}
