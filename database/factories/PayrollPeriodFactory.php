<?php

namespace Database\Factories;

use App\Models\PayrollPeriod;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PayrollPeriod>
 */
class PayrollPeriodFactory extends Factory
{
    protected $model = PayrollPeriod::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('-2 months', 'now');
        $endDate = Carbon::parse($startDate)->endOfMonth();
        
        return [
            'nama_periode' => 'Payroll ' . Carbon::parse($startDate)->format('F Y'),
            'tanggal_mulai' => $startDate,
            'tanggal_selesai' => $endDate,
            'tanggal_cutoff' => $endDate,
            'status' => $this->faker->randomElement(['draft', 'processing', 'completed', 'cancelled']),
            'keterangan' => $this->faker->optional()->sentence(),
            'karyawan_ids' => null, // Will be set manually in tests
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the payroll period is in draft status.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Indicate that the payroll period is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'processed_by' => User::factory(),
            'processed_at' => now(),
        ]);
    }

    /**
     * Indicate that the payroll period is processing.
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processing',
            'processed_by' => User::factory(),
            'processed_at' => now(),
        ]);
    }

    /**
     * Set specific karyawan IDs for the period.
     */
    public function withKaryawan(array $karyawanIds): static
    {
        return $this->state(fn (array $attributes) => [
            'karyawan_ids' => $karyawanIds,
        ]);
    }
}
