<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Lembur;
use Illuminate\Support\Facades\DB;

echo "=== DEBUG LEMBUR BARU APPROVAL PROCESS ===\n\n";

// Ambil lembur terbaru
$latestLembur = Lembur::with(['approvalStatus', 'approvals', 'karyawan'])
    ->orderBy('created_at', 'desc')
    ->first();

if (!$latestLembur) {
    echo "Tidak ada data lembur ditemukan\n";
    exit;
}

echo "=== LEMBUR TERBARU ===\n";
echo "ID: {$latestLembur->id}\n";
echo "Karyawan: {$latestLembur->karyawan->nama_lengkap}\n";
echo "Tanggal: {$latestLembur->tanggal}\n";
echo "Jumlah Jam: {$latestLembur->jumlah_jam}\n";
echo "Created At: {$latestLembur->created_at}\n";
echo "Created By: {$latestLembur->created_by}\n\n";

// Cek approval status
echo "=== APPROVAL STATUS ===\n";
if ($latestLembur->approvalStatus) {
    echo "Status: {$latestLembur->approvalStatus->status}\n";
    echo "Creator ID: {$latestLembur->approvalStatus->creator_id}\n";
    echo "Created At: {$latestLembur->approvalStatus->created_at}\n";
    echo "Updated At: {$latestLembur->approvalStatus->updated_at}\n\n";
} else {
    echo "❌ TIDAK ADA APPROVAL STATUS!\n";
    echo "Ini menunjukkan lembur tidak masuk ke sistem approval\n\n";
}

// Cek approval records
echo "=== APPROVAL RECORDS ===\n";
if ($latestLembur->approvals && $latestLembur->approvals->count() > 0) {
    foreach ($latestLembur->approvals as $approval) {
        echo "- Action: {$approval->approval_action}\n";
        echo "  User ID: {$approval->user_id}\n";
        echo "  Created: {$approval->created_at}\n";
        echo "  Comment: " . ($approval->comment ?: '(kosong)') . "\n\n";
    }
} else {
    echo "❌ TIDAK ADA APPROVAL RECORDS!\n";
    echo "Lembur belum di-submit untuk approval\n\n";
}

// Cek approval flow configuration
echo "=== APPROVAL FLOW CONFIGURATION ===\n";
$approvalFlow = DB::table('process_approval_flows')
    ->where('approvable_type', 'App\\Models\\Lembur')
    ->first();

if ($approvalFlow) {
    echo "✅ Approval Flow EXISTS\n";
    echo "ID: {$approvalFlow->id}\n";
    echo "Name: {$approvalFlow->name}\n";
    if (isset($approvalFlow->active)) {
        echo "Active: " . ($approvalFlow->active ? 'Yes' : 'No') . "\n";
    }
    echo "\n";

    // Cek steps
    $steps = DB::table('process_approval_flow_steps')
        ->where('process_approval_flow_id', $approvalFlow->id)
        ->orderBy('order')
        ->get();

    echo "Approval Steps:\n";
    if ($steps->count() > 0) {
        foreach ($steps as $step) {
            echo "- Order: {$step->order}";
            if (isset($step->role_id)) echo ", Role ID: {$step->role_id}";
            if (isset($step->user_id)) echo ", User ID: {$step->user_id}";
            echo "\n";
        }
    } else {
        echo "❌ TIDAK ADA APPROVAL STEPS!\n";
        echo "Approval flow tidak memiliki steps yang dikonfigurasi\n";
    }
    echo "\n";
} else {
    echo "❌ TIDAK ADA APPROVAL FLOW!\n";
    echo "Approval flow untuk Lembur belum dikonfigurasi\n\n";
}

// Cek method approval di model
echo "=== MODEL APPROVAL METHODS ===\n";
echo "isSubmitted(): " . ($latestLembur->isSubmitted() ? 'true' : 'false') . "\n";
echo "isApprovalCompleted(): " . ($latestLembur->isApprovalCompleted() ? 'true' : 'false') . "\n";

try {
    echo "isRejected(): " . ($latestLembur->isRejected() ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "isRejected(): method not available\n";
}

try {
    echo "isDiscarded(): " . ($latestLembur->isDiscarded() ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "isDiscarded(): method not available\n";
}

// Cek apakah bisa di-submit
try {
    $canSubmit = !$latestLembur->isSubmitted();
    echo "Can be submitted: " . ($canSubmit ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "Can be submitted: error checking - {$e->getMessage()}\n";
}

echo "\n";

// Analisis masalah
echo "=== ANALISIS MASALAH ===\n";

$hasApprovalFlow = $approvalFlow !== null;
$hasApprovalSteps = $approvalFlow && $steps->count() > 0;
$hasApprovalStatus = $latestLembur->approvalStatus !== null;
$isSubmitted = $latestLembur->isSubmitted();

echo "✓ Approval Flow configured: " . ($hasApprovalFlow ? 'YES' : 'NO') . "\n";
echo "✓ Approval Steps configured: " . ($hasApprovalSteps ? 'YES' : 'NO') . "\n";
echo "✓ Approval Status exists: " . ($hasApprovalStatus ? 'YES' : 'NO') . "\n";
echo "✓ Lembur submitted: " . ($isSubmitted ? 'YES' : 'NO') . "\n\n";

// Diagnosis
if (!$hasApprovalFlow) {
    echo "🔥 MASALAH UTAMA: Approval flow untuk Lembur belum dikonfigurasi\n";
    echo "SOLUSI: Buat approval flow untuk model Lembur di admin panel\n\n";
} else if (!$hasApprovalSteps) {
    echo "🔥 MASALAH UTAMA: Approval flow tidak memiliki steps\n";
    echo "SOLUSI: Tambahkan approval steps (role/user) ke approval flow\n\n";
} else if (!$hasApprovalStatus) {
    echo "🔥 MASALAH UTAMA: Lembur tidak masuk ke sistem approval\n";
    echo "SOLUSI: Pastikan lembur di-submit untuk approval\n\n";
} else if (!$isSubmitted) {
    echo "🔥 MASALAH UTAMA: Lembur belum di-submit untuk approval\n";
    echo "SOLUSI: Submit lembur melalui UI atau programmatically\n\n";
} else {
    echo "✅ KONFIGURASI TERLIHAT BENAR\n";
    echo "Lembur sudah di-submit dan menunggu approval dari approver\n";
    echo "Status 'Pending' adalah normal untuk lembur yang menunggu approval\n\n";
}

// Cek siapa yang bisa approve
if ($hasApprovalFlow && $hasApprovalSteps) {
    echo "=== APPROVER INFORMATION ===\n";

    foreach ($steps as $step) {
        echo "Step {$step->order}:\n";

        if (isset($step->role_id) && $step->role_id) {
            $role = DB::table('roles')->where('id', $step->role_id)->first();
            if ($role) {
                echo "- Role: {$role->name}\n";

                // Cek users dengan role ini
                $usersWithRole = DB::table('model_has_roles')
                    ->join('users', 'model_has_roles.model_id', '=', 'users.id')
                    ->where('model_has_roles.role_id', $step->role_id)
                    ->where('model_has_roles.model_type', 'App\\Models\\User')
                    ->select('users.id', 'users.name', 'users.email')
                    ->get();

                if ($usersWithRole->count() > 0) {
                    echo "- Users with this role:\n";
                    foreach ($usersWithRole as $user) {
                        echo "  * {$user->name} (ID: {$user->id}, Email: {$user->email})\n";
                    }
                } else {
                    echo "- ❌ NO USERS with this role!\n";
                }
            }
        }

        if (isset($step->user_id) && $step->user_id) {
            $user = DB::table('users')->where('id', $step->user_id)->first();
            if ($user) {
                echo "- Specific User: {$user->name} (ID: {$user->id})\n";
            }
        }

        echo "\n";
    }
}

echo "=== REKOMENDASI ===\n";
echo "1. Pastikan approval flow untuk Lembur sudah dikonfigurasi dengan benar\n";
echo "2. Pastikan ada users yang memiliki role approver\n";
echo "3. Submit lembur untuk approval jika belum\n";
echo "4. Approver melakukan approval melalui UI\n";
echo "5. Monitor log aplikasi untuk error approval\n";
