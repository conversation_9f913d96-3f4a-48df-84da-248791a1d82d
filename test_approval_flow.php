<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Lembur;
use App\Models\Karyawan;
use App\Models\JenisLembur;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

echo "=== TEST APPROVAL FLOW END-TO-END ===\n\n";

// 1. Buat lembur baru
echo "=== STEP 1: BUAT LEMBUR BARU ===\n";

$karyawan = Karyawan::where('status_aktif', true)->first();
$jenisLembur = JenisLembur::first();

if (!$karyawan || !$jenisLembur) {
    echo "❌ Tidak ada karyawan atau jenis lembur\n";
    exit;
}

// Login sebagai karyawan untuk membuat lembur
Auth::loginUsingId($karyawan->id_user);

$lembur = Lembur::create([
    'karyawan_id' => $karyawan->id,
    'tanggal' => now()->format('Y-m-d'),
    'jumlah_jam' => 2.5,
    'jenis_lembur_id' => $jenisLembur->id,
    'keterangan' => 'Test lembur untuk approval flow',
    'created_by' => $karyawan->id_user,
]);

echo "✅ Lembur berhasil dibuat (ID: {$lembur->id})\n";
echo "Karyawan: {$karyawan->nama_lengkap}\n";
echo "Tanggal: {$lembur->tanggal}\n";
echo "Jumlah Jam: {$lembur->jumlah_jam}\n\n";

// 2. Submit untuk approval
echo "=== STEP 2: SUBMIT UNTUK APPROVAL ===\n";

try {
    $submitResult = $lembur->submit(user: Auth::user());
    echo "Submit result: " . ($submitResult ? 'true' : 'false') . "\n";
    
    $lembur->refresh();
    echo "Status setelah submit: " . ($lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status') . "\n";
    echo "isSubmitted(): " . ($lembur->isSubmitted() ? 'true' : 'false') . "\n";
    echo "isApprovalCompleted(): " . ($lembur->isApprovalCompleted() ? 'true' : 'false') . "\n\n";
    
} catch (Exception $e) {
    echo "❌ Error saat submit: {$e->getMessage()}\n";
    exit;
}

// 3. Login sebagai approver
echo "=== STEP 3: LOGIN SEBAGAI APPROVER ===\n";

$managerHrd = DB::table('model_has_roles')
    ->join('users', 'model_has_roles.model_id', '=', 'users.id')
    ->where('model_has_roles.role_id', 2) // manager_hrd
    ->where('model_has_roles.model_type', 'App\\Models\\User')
    ->select('users.*')
    ->first();

if (!$managerHrd) {
    echo "❌ Tidak ada user dengan role manager_hrd\n";
    exit;
}

Auth::loginUsingId($managerHrd->id);
echo "✅ Login sebagai: {$managerHrd->name} (ID: {$managerHrd->id})\n";

// 4. Cek apakah bisa approve
echo "canBeApprovedBy(): " . ($lembur->canBeApprovedBy(Auth::user()) ? 'true' : 'false') . "\n\n";

if (!$lembur->canBeApprovedBy(Auth::user())) {
    echo "❌ User tidak bisa approve lembur ini\n";
    exit;
}

// 5. Lakukan approval
echo "=== STEP 4: LAKUKAN APPROVAL ===\n";

echo "Status sebelum approval:\n";
echo "- Approval Status: " . ($lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status') . "\n";
echo "- Total approval records: " . ($lembur->approvals ? $lembur->approvals->count() : 0) . "\n";

try {
    DB::beginTransaction();
    
    // Monitor database sebelum approval
    $statusBefore = DB::table('process_approval_statuses')
        ->where('approvable_type', 'App\\Models\\Lembur')
        ->where('approvable_id', $lembur->id)
        ->first();
    
    $approvalsBefore = DB::table('process_approvals')
        ->where('approvable_type', 'App\\Models\\Lembur')
        ->where('approvable_id', $lembur->id)
        ->count();
    
    echo "\nDatabase sebelum approval:\n";
    echo "- Status: " . ($statusBefore ? $statusBefore->status : 'No status') . "\n";
    echo "- Approval records: {$approvalsBefore}\n\n";
    
    // Lakukan approval
    echo "Melakukan approval...\n";
    $approvalResult = $lembur->approve(comment: 'Approved for testing', user: Auth::user());
    echo "Approval result: " . ($approvalResult ? 'true' : 'false') . "\n";
    
    // Monitor database setelah approval
    $statusAfter = DB::table('process_approval_statuses')
        ->where('approvable_type', 'App\\Models\\Lembur')
        ->where('approvable_id', $lembur->id)
        ->first();
    
    $approvalsAfter = DB::table('process_approvals')
        ->where('approvable_type', 'App\\Models\\Lembur')
        ->where('approvable_id', $lembur->id)
        ->count();
    
    echo "\nDatabase setelah approval:\n";
    echo "- Status: " . ($statusAfter ? $statusAfter->status : 'No status') . "\n";
    echo "- Approval records: {$approvalsAfter}\n";
    
    // Refresh model
    $lembur->refresh();
    
    echo "\nModel setelah refresh:\n";
    echo "- Status: " . ($lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status') . "\n";
    echo "- isApprovalCompleted(): " . ($lembur->isApprovalCompleted() ? 'true' : 'false') . "\n";
    echo "- Total approval records: " . ($lembur->approvals ? $lembur->approvals->count() : 0) . "\n";
    
    DB::commit();
    
} catch (Exception $e) {
    DB::rollback();
    echo "❌ Error saat approval: {$e->getMessage()}\n";
    echo "Stack trace:\n{$e->getTraceAsString()}\n";
    exit;
}

// 6. Analisis hasil
echo "\n=== STEP 5: ANALISIS HASIL ===\n";

$finalStatus = $lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status';
$hasApprovedRecord = $lembur->approvals()->where('approval_action', 'Approved')->exists();
$isCompleted = $lembur->isApprovalCompleted();

echo "Final status: {$finalStatus}\n";
echo "Has approved record: " . ($hasApprovedRecord ? 'true' : 'false') . "\n";
echo "Is approval completed: " . ($isCompleted ? 'true' : 'false') . "\n\n";

if ($hasApprovedRecord && $finalStatus === 'Pending') {
    echo "🐛 BUG CONFIRMED: Package tidak mengupdate status dengan benar!\n";
    echo "Approval record dibuat tapi status masih Pending\n\n";
    
    echo "IMPLEMENTASI FIX:\n";
    echo "Akan membuat override method approve() di model Lembur\n";
    
} else if ($finalStatus === 'Approved' && $isCompleted) {
    echo "✅ APPROVAL FLOW BERHASIL: Semua berjalan dengan benar!\n";
    
} else {
    echo "⚠️  STATUS TIDAK KONSISTEN: Perlu investigasi lebih lanjut\n";
    echo "Expected: Status = Approved, Completed = true\n";
    echo "Actual: Status = {$finalStatus}, Completed = " . ($isCompleted ? 'true' : 'false') . "\n";
}

// 7. Test apakah lembur akan masuk ke payroll
echo "\n=== STEP 6: TEST FILTER PAYROLL ===\n";

$shouldIncludeInPayroll = false;

if (!$lembur->approvalStatus) {
    $shouldIncludeInPayroll = true;
    echo "Logic: Tidak ada approval status -> Include (legacy data)\n";
} else {
    $isCompleted = $lembur->isApprovalCompleted();
    $hasApprovedRecord = $lembur->approvals()->where('approval_action', 'Approved')->exists();
    
    if ($isCompleted) {
        $shouldIncludeInPayroll = $hasApprovedRecord;
        echo "Logic: isApprovalCompleted() = true, hasApprovedRecord = " . ($hasApprovedRecord ? 'true' : 'false') . "\n";
    } else {
        $shouldIncludeInPayroll = $lembur->approvalStatus->status === 'Approved';
        echo "Logic: isApprovalCompleted() = false, status = {$lembur->approvalStatus->status}\n";
    }
}

echo "Will be included in payroll: " . ($shouldIncludeInPayroll ? 'YES' : 'NO') . "\n\n";

if ($shouldIncludeInPayroll) {
    echo "✅ LEMBUR AKAN MASUK KE PAYROLL\n";
} else {
    echo "❌ LEMBUR TIDAK AKAN MASUK KE PAYROLL\n";
}

echo "\nTest selesai. Lembur ID: {$lembur->id}\n";
