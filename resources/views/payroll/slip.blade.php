<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slip <PERSON>i - {{ $payroll->karyawan->nama_lengkap }}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
            font-size: 12px;
            line-height: 1.4;
            color: #1a202c;
        }

        .slip-container {
            max-width: 210mm;
            margin: 0 auto;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            min-height: 297mm;
            position: relative;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 3px solid #3182ce;
            padding-bottom: 15px;
            margin-bottom: 20px;
            background: linear-gradient(90deg, #3182ce 0%, #2c5aa0 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin: -20px -20px 20px -20px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            width: 50px;
            height: 50px;
            background: white;
            border-radius: 8px;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .company-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .header-text {
            text-align: left;
        }

        .company-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 3px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .slip-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 2px;
        }

        .period {
            font-size: 12px;
            opacity: 0.8;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        .employee-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            border-radius: 8px;
            border: 1px solid #cbd5e0;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            padding: 2px 0;
        }

        .info-label {
            font-weight: 600;
            color: #4a5568;
            font-size: 11px;
        }

        .info-value {
            color: #2d3748;
            font-weight: 500;
            font-size: 11px;
        }

        .salary-section {
            margin-bottom: 12px;
            page-break-inside: avoid;
        }

        .two-column-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 12px;
        }

        .compact-section {
            margin-bottom: 8px;
        }

        .section-title {
            font-size: 13px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: linear-gradient(90deg, #3182ce 0%, #2c5aa0 100%);
            color: white;
            border-radius: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .salary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .salary-table th,
        .salary-table td {
            padding: 6px 10px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
            font-size: 11px;
        }

        .salary-table th {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            font-weight: 600;
            color: #4a5568;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        .total-row {
            font-weight: bold;
            background: linear-gradient(135deg, #bee3f8 0%, #90cdf4 100%);
            color: #1a365d;
        }

        .take-home-pay {
            background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
            font-size: 14px;
            font-weight: bold;
            color: #1a202c;
            border: 2px solid #48bb78;
        }

        .detail-section {
            margin-top: 10px;
            font-size: 10px;
        }

        .detail-section th {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            font-size: 10px;
            padding: 5px 8px;
            font-weight: 600;
            color: #4a5568;
        }

        .detail-section td {
            padding: 4px 8px;
            font-size: 10px;
        }

        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
            color: #718096;
            border-top: 2px solid #e2e8f0;
            padding-top: 15px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 6px;
            padding: 15px;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(49, 130, 206, 0.05);
            font-weight: bold;
            z-index: 0;
            pointer-events: none;
        }

        .content-wrapper {
            position: relative;
            z-index: 1;
        }

        @media print {
            @page {
                size: A4;
                margin: 8mm;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            body {
                background-color: white !important;
                padding: 0 !important;
                font-size: 10px !important;
                line-height: 1.3 !important;
            }

            .slip-container {
                box-shadow: none !important;
                padding: 10px !important;
                margin: 0 !important;
                border: none !important;
                border-radius: 0 !important;
                background: white !important;
                max-width: none !important;
                width: 100% !important;
                min-height: auto !important;
            }

            .header {
                margin: -10px -10px 12px -10px !important;
                padding: 10px 12px !important;
                background: #3182ce !important;
                color: white !important;
                border-radius: 0 !important;
            }

            .company-logo {
                width: 40px !important;
                height: 40px !important;
            }

            .company-name {
                font-size: 16px !important;
            }

            .slip-title {
                font-size: 12px !important;
            }

            .period {
                font-size: 10px !important;
            }

            .section-title {
                background: #3182ce !important;
                color: white !important;
                font-size: 11px !important;
                padding: 6px 10px !important;
            }

            .salary-table th,
            .salary-table td {
                padding: 4px 8px !important;
                font-size: 9px !important;
            }

            .total-row {
                background: #bee3f8 !important;
                color: #1a365d !important;
            }

            .take-home-pay {
                background: #c6f6d5 !important;
                border: 2px solid #48bb78 !important;
                color: #1a202c !important;
                font-size: 12px !important;
            }

            .employee-info {
                background: #edf2f7 !important;
                padding: 10px !important;
            }

            .info-label,
            .info-value {
                font-size: 9px !important;
            }

            .footer {
                background: #f7fafc !important;
                margin-top: 15px !important;
                padding: 10px !important;
                font-size: 8px !important;
            }

            .watermark {
                display: none !important;
            }

            .two-column-layout {
                gap: 10px !important;
            }

            .detail-section th,
            .detail-section td {
                padding: 3px 6px !important;
                font-size: 8px !important;
            }

            .salary-section {
                margin-bottom: 8px !important;
            }

            .compact-section {
                margin-bottom: 6px !important;
            }
        }

        @media screen {
            .slip-container {
                margin: 20px auto;
            }
        }
    </style>
</head>

<body>
    <div class="slip-container">
        <!-- Watermark -->
        <div class="watermark">PT. VIERA</div>

        <div class="content-wrapper">
            <!-- Header -->
            <div class="header">
                <div class="header-left">
                    <div class="company-logo">
                        <img src="{{ asset('images/viera-logo.png') }}" alt="PT. Viera Logo" onerror="this.style.display='none'">
                    </div>
                    <div class="header-text">
                        <div class="company-name">PT. VIERA ANUGRAH PERTAMA</div>
                        <div class="slip-title">SLIP GAJI KARYAWAN</div>
                    </div>
                </div>
                <div class="period">Periode: {{ $payroll->payrollPeriod->formatted_periode }}</div>
            </div>

            <!-- Employee Information -->
            <div class="employee-info">
                <div>
                    <div class="info-item">
                        <span class="info-label">Nama Karyawan:</span>
                        <span class="info-value">{{ $payroll->karyawan->nama_lengkap }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">NIP:</span>
                        <span class="info-value">{{ $payroll->karyawan->nip }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Jabatan:</span>
                        <span class="info-value">{{ $payroll->karyawan->jabatan->nama_jabatan ?? '-' }}</span>
                    </div>
                </div>
                <div>
                    <div class="info-item">
                        <span class="info-label">No. Payroll:</span>
                        <span class="info-value">{{ $payroll->no_payroll }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Departemen:</span>
                        <span class="info-value">{{ $payroll->karyawan->departemen->nama_departemen ?? '-' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Tanggal Cetak:</span>
                        <span class="info-value">{{ now()->format('d M Y H:i') }}</span>
                    </div>
                </div>
            </div>

            <!-- Salary Components -->
            <div class="salary-section">
                <div class="section-title">💰 KOMPONEN GAJI</div>
            <table class="salary-table">
                <thead>
                    <tr>
                        <th>Keterangan</th>
                        <th class="amount">Jumlah (Rp)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Gaji Pokok</td>
                        <td class="amount">{{ number_format($payroll->gaji_pokok, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Tunjangan Jabatan</td>
                        <td class="amount">{{ number_format($payroll->tunjangan_jabatan, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Tunjangan Umum</td>
                        <td class="amount">{{ number_format($payroll->tunjangan_umum, 0, ',', '.') }}</td>
                    </tr>
                    @if ($payroll->insentif > 0)
                        <tr>
                            <td>Insentif</td>
                            <td class="amount">{{ number_format($payroll->insentif, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    <tr>
                        <td>Tunjangan Sembako</td>
                        <td class="amount">{{ number_format($payroll->tunjangan_sembako, 0, ',', '.') }}</td>
                    </tr>
                    @if ($payroll->lembur_biasa > 0)
                        <tr>
                            <td>Lembur Biasa</td>
                            <td class="amount">{{ number_format($payroll->lembur_biasa, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->lembur_tanggal_merah > 0)
                        <tr>
                            <td>Lembur Tanggal Merah</td>
                            <td class="amount">{{ number_format($payroll->lembur_tanggal_merah, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->lembur_tambah_hk > 0)
                        <tr>
                            <td>Lembur Tambah HK</td>
                            <td class="amount">{{ number_format($payroll->lembur_tambah_hk, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->kekurangan_gaji_bulan_sebelum > 0)
                        <tr>
                            <td>Kekurangan Gaji Bulan Sebelum</td>
                            <td class="amount">
                                {{ number_format($payroll->kekurangan_gaji_bulan_sebelum, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    @if ($payroll->claim_sakit_dengan_surat > 0)
                        <tr>
                            <td>Claim Sakit Dengan Surat</td>
                            <td class="amount">{{ number_format($payroll->claim_sakit_dengan_surat, 0, ',', '.') }}
                            </td>
                        </tr>
                    @endif
                    @if ($payroll->pesangon > 0)
                        <tr>
                            <td>Pesangon</td>
                            <td class="amount">{{ number_format($payroll->pesangon, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    <tr class="total-row">
                        <td><strong>Total Gaji Kotor</strong></td>
                        <td class="amount">
                            <strong>{{ number_format($payroll->total_gaji_kotor, 0, ',', '.') }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
            </div>

            <!-- Deductions -->
            <div class="salary-section">
                <div class="section-title">✂️ POTONGAN</div>
            <table class="salary-table">
                <thead>
                    <tr>
                        <th>Keterangan</th>
                        <th class="amount">Jumlah (Rp)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>BPJS Kesehatan</td>
                        <td class="amount">{{ number_format($payroll->potongan_bpjs_kesehatan, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>BPJS Tenaga Kerja</td>
                        <td class="amount">{{ number_format($payroll->potongan_bpjs_tk, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Potongan Keterlambatan</td>
                        <td class="amount">{{ number_format($payroll->potongan_keterlambatan, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Potongan Pelanggaran</td>
                        <td class="amount">{{ number_format($payroll->potongan_pelanggaran, 0, ',', '.') }}</td>
                    </tr>
                    @php
                        // Group potongan by jenis
                        $potonganByJenis = $payroll->payrollDeductions->groupBy('jenis_potongan');

                        // Hitung total potongan karyawan (kasir, stok_opname, retur, kasbon)
                        $potonganKaryawan = $potonganByJenis->filter(function ($group, $jenis) {
                            return in_array($jenis, ['kasir', 'stok_opname', 'retur', 'kasbon']);
                        });

                        // Hitung total potongan absensi (sakit tanpa surat, alpha, cuti melebihi kuota)
                        $potonganAbsensi = $potonganByJenis->filter(function ($group, $jenis) {
                            return in_array($jenis, ['sakit_tanpa_surat', 'alpha', 'cuti_melebihi_kuota']);
                        });

                        // Hitung potongan lainnya yang bukan dari jenis karyawan dan absensi
                        $potonganLainnyaActual = $payroll->potongan_lainnya;
                        $totalPotonganKaryawan = $potonganKaryawan->flatten()->sum('nominal');
                        $totalPotonganAbsensi = $potonganAbsensi->flatten()->sum('nominal');
                        $sisaPotonganLainnya = $potonganLainnyaActual - $totalPotonganKaryawan - $totalPotonganAbsensi;
                    @endphp

                    {{-- Tampilkan potongan karyawan berdasarkan jenis --}}
                    @foreach ($potonganKaryawan as $jenis => $deductions)
                        <tr>
                            <td>{{ $deductions->first()->jenis_label }}</td>
                            <td class="amount">{{ number_format($deductions->sum('nominal'), 0, ',', '.') }}</td>
                        </tr>
                    @endforeach

                    {{-- Tampilkan potongan absensi berdasarkan jenis --}}
                    @foreach ($potonganAbsensi as $jenis => $deductions)
                        <tr>
                            <td>{{ $deductions->first()->jenis_label }}</td>
                            <td class="amount">{{ number_format($deductions->sum('nominal'), 0, ',', '.') }}</td>
                        </tr>
                    @endforeach

                    {{-- Tampilkan sisa potongan lainnya jika ada --}}
                    @if ($sisaPotonganLainnya > 0)
                        <tr>
                            <td>Potongan Lainnya</td>
                            <td class="amount">{{ number_format($sisaPotonganLainnya, 0, ',', '.') }}</td>
                        </tr>
                    @endif
                    <tr class="total-row">
                        <td><strong>Total Potongan</strong></td>
                        <td class="amount"><strong>{{ number_format($payroll->total_potongan, 0, ',', '.') }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
            </div>

            {{-- Detail Potongan Karyawan jika ada --}}
            @if ($potonganKaryawan->count() > 0)
                <div class="salary-section detail-section">
                    <div class="section-title">📋 DETAIL POTONGAN KARYAWAN</div>
                <table class="salary-table">
                    <thead>
                        <tr>
                            <th>Jenis Potongan</th>
                            <th>Tanggal</th>
                            <th>Keterangan</th>
                            <th class="amount">Jumlah (Rp)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($potonganKaryawan->flatten() as $deduction)
                            <tr>
                                <td>{{ $deduction->jenis_label }}</td>
                                <td>{{ $deduction->tanggal_kejadian ? $deduction->tanggal_kejadian->format('d M Y') : '-' }}
                                </td>
                                <td>{{ $deduction->keterangan ?: $deduction->deskripsi }}</td>
                                <td class="amount">{{ number_format($deduction->nominal, 0, ',', '.') }}</td>
                            </tr>
                        @endforeach
                        <tr class="total-row">
                            <td colspan="3"><strong>Total Potongan Karyawan</strong></td>
                            <td class="amount">
                                <strong>{{ number_format($totalPotonganKaryawan, 0, ',', '.') }}</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            @endif

            {{-- Detail Potongan Absensi jika ada --}}
            @if ($potonganAbsensi->count() > 0)
                <div class="salary-section detail-section">
                    <div class="section-title">📅 DETAIL POTONGAN ABSENSI</div>
                <table class="salary-table">
                    <thead>
                        <tr>
                            <th>Jenis Potongan</th>
                            <th>Tanggal</th>
                            <th>Keterangan</th>
                            <th class="amount">Jumlah (Rp)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($potonganAbsensi->flatten() as $deduction)
                            <tr>
                                <td>{{ $deduction->jenis_label }}</td>
                                <td>{{ $deduction->tanggal_kejadian ? $deduction->tanggal_kejadian->format('d M Y') : '-' }}
                                </td>
                                <td>{{ $deduction->keterangan ?: $deduction->deskripsi }}</td>
                                <td class="amount">{{ number_format($deduction->nominal, 0, ',', '.') }}</td>
                            </tr>
                        @endforeach
                        <tr class="total-row">
                            <td colspan="3"><strong>Total Potongan Absensi</strong></td>
                            <td class="amount"><strong>{{ number_format($totalPotonganAbsensi, 0, ',', '.') }}</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            @endif

            <!-- Take Home Pay & Attendance Summary in 2 columns -->
            <div class="two-column-layout">
                <!-- Take Home Pay -->
                <div class="salary-section compact-section">
                    <table class="salary-table">
                        <tbody>
                            <tr class="take-home-pay">
                                <td><strong>💵 TAKE HOME PAY</strong></td>
                                <td class="amount"><strong>Rp {{ number_format($payroll->take_home_pay, 0, ',', '.') }}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Attendance Summary -->
                <div class="salary-section compact-section">
                    <div class="section-title">📊 RINGKASAN ABSENSI</div>
                    <table class="salary-table">
                        <tbody>
                            <tr>
                                <td>Total Hari Kerja</td>
                                <td class="amount">{{ $payroll->total_hari_kerja }} hari</td>
                            </tr>
                            <tr>
                                <td>Total Hari Hadir</td>
                                <td class="amount">{{ $payroll->total_hari_hadir }} hari</td>
                            </tr>
                            <tr>
                                <td>Total Menit Terlambat</td>
                                <td class="amount">{{ $payroll->total_menit_terlambat }} menit</td>
                            </tr>
                            <tr>
                                <td>Total Pelanggaran</td>
                                <td class="amount">{{ $payroll->total_pelanggaran }} kasus</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Footer -->
            <div class="footer">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <div style="text-align: left;">
                        <strong>PT. VIERA ANUGRAH PERTAMA</strong><br>
                        <small>Human Resources Department</small>
                    </div>
                    <div style="text-align: right;">
                        <small>Generated: {{ now()->format('d M Y H:i:s') }}</small><br>
                        <small>Document ID: {{ $payroll->no_payroll }}</small>
                    </div>
                </div>
                <hr style="border: none; border-top: 1px solid #cbd5e0; margin: 10px 0;">
                <p style="margin: 0; text-align: center;">
                    <small>📞 Untuk pertanyaan terkait gaji, silakan hubungi bagian HR |
                    🔒 Dokumen ini bersifat rahasia dan hanya untuk karyawan yang bersangkutan</small>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Enhanced print handling
        window.onload = function() {
            // Add print button for manual printing
            const printButton = document.createElement('button');
            printButton.innerHTML = '🖨️ Print Slip Gaji';
            printButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3182ce;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            printButton.onclick = function() {
                window.print();
            };

            // Only show print button on screen, not in print
            const style = document.createElement('style');
            style.textContent = '@media print { .print-button { display: none !important; } }';
            document.head.appendChild(style);
            printButton.className = 'print-button';

            document.body.appendChild(printButton);

            // Auto print after a short delay to ensure everything is loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };

        // Handle print events
        window.addEventListener('beforeprint', function() {
            console.log('Preparing to print...');
        });

        window.addEventListener('afterprint', function() {
            console.log('Print dialog closed');
        });

        // Ensure proper print preview
        if (window.matchMedia) {
            const mediaQueryList = window.matchMedia('print');
            mediaQueryList.addListener(function(mql) {
                if (mql.matches) {
                    console.log('Print mode activated');
                } else {
                    console.log('Print mode deactivated');
                }
            });
        }
    </script>
</body>

</html>
