<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Lembur;
use Illuminate\Support\Facades\DB;

// Ambil Lembur ID 5 yang bermasalah
$lembur = Lembur::with(['approvalStatus', 'approvals'])->find(5);

if (!$lembur) {
    echo "Lembur ID 5 tidak ditemukan\n";
    exit;
}

echo "=== ANALISIS BUG APPROVAL LEMBUR ===\n\n";

echo "Lembur ID: {$lembur->id}\n";
echo "Tanggal: {$lembur->tanggal}\n";
echo "Karyawan ID: {$lembur->karyawan_id}\n\n";

// 1. Cek data di process_approval_statuses
echo "=== DATA PROCESS_APPROVAL_STATUSES ===\n";
if ($lembur->approvalStatus) {
    echo "ID: {$lembur->approvalStatus->id}\n";
    echo "Status: {$lembur->approvalStatus->status}\n";
    echo "Creator ID: {$lembur->approvalStatus->creator_id}\n";
    echo "Created At: {$lembur->approvalStatus->created_at}\n";
    echo "Updated At: {$lembur->approvalStatus->updated_at}\n\n";
} else {
    echo "Tidak ada data di process_approval_statuses\n\n";
}

// 2. Cek data di process_approvals (approval records)
echo "=== DATA PROCESS_APPROVALS ===\n";
if ($lembur->approvals && $lembur->approvals->count() > 0) {
    foreach ($lembur->approvals as $approval) {
        echo "--- Approval Record #{$approval->id} ---\n";
        echo "Action: {$approval->approval_action}\n";
        echo "User ID: {$approval->user_id}\n";
        echo "Comment: " . ($approval->comment ?: '(kosong)') . "\n";
        echo "Created At: {$approval->created_at}\n";
        echo "Updated At: {$approval->updated_at}\n\n";
    }
} else {
    echo "Tidak ada data di process_approvals\n\n";
}

// 3. Cek raw data dari database
echo "=== RAW DATABASE QUERY ===\n";

// Query process_approval_statuses
$approvalStatus = DB::table('process_approval_statuses')
    ->where('approvable_type', 'App\\Models\\Lembur')
    ->where('approvable_id', 5)
    ->first();

if ($approvalStatus) {
    echo "process_approval_statuses:\n";
    echo "- ID: {$approvalStatus->id}\n";
    echo "- Status: {$approvalStatus->status}\n";
    echo "- Creator ID: {$approvalStatus->creator_id}\n";
    echo "- Created: {$approvalStatus->created_at}\n";
    echo "- Updated: {$approvalStatus->updated_at}\n\n";
} else {
    echo "Tidak ada data di process_approval_statuses untuk lembur ID 5\n\n";
}

// Query process_approvals
$approvals = DB::table('process_approvals')
    ->where('approvable_type', 'App\\Models\\Lembur')
    ->where('approvable_id', 5)
    ->orderBy('created_at')
    ->get();

if ($approvals->count() > 0) {
    echo "process_approvals:\n";
    foreach ($approvals as $approval) {
        echo "- ID: {$approval->id}, Action: {$approval->approval_action}, User: {$approval->user_id}, Created: {$approval->created_at}\n";
    }
    echo "\n";
} else {
    echo "Tidak ada data di process_approvals untuk lembur ID 5\n\n";
}

// 4. Analisis masalah
echo "=== ANALISIS MASALAH ===\n";

$hasApprovedAction = $approvals->where('approval_action', 'Approved')->count() > 0;
$statusIsPending = $approvalStatus && $approvalStatus->status === 'Pending';

echo "Ada approval action 'Approved': " . ($hasApprovedAction ? 'YA' : 'TIDAK') . "\n";
echo "Status di approval_statuses: " . ($approvalStatus ? $approvalStatus->status : 'TIDAK ADA') . "\n";
echo "isApprovalCompleted(): " . ($lembur->isApprovalCompleted() ? 'TRUE' : 'FALSE') . "\n\n";

if ($hasApprovedAction && $statusIsPending) {
    echo "🐛 BUG DITEMUKAN:\n";
    echo "- Ada approval record dengan action 'Approved'\n";
    echo "- Tapi status di process_approval_statuses masih 'Pending'\n";
    echo "- Ini menunjukkan status tidak terupdate setelah approval\n\n";

    echo "KEMUNGKINAN PENYEBAB:\n";
    echo "1. Bug dalam sistem approval yang tidak mengupdate status\n";
    echo "2. Approval flow tidak selesai dengan benar\n";
    echo "3. Ada error saat mengupdate status tapi approval record tetap tersimpan\n";
    echo "4. Race condition atau transaction yang tidak complete\n\n";

    // Cek approval flow
    echo "=== CEK APPROVAL FLOW ===\n";
    $approvalFlow = DB::table('process_approval_flows')
        ->where('approvable_type', 'App\\Models\\Lembur')
        ->first();

    if ($approvalFlow) {
        echo "Approval Flow ID: {$approvalFlow->id}\n";
        echo "Name: {$approvalFlow->name}\n";

        // Cek steps
        $steps = DB::table('process_approval_flow_steps')
            ->where('process_approval_flow_id', $approvalFlow->id)
            ->orderBy('order')
            ->get();

        echo "Steps:\n";
        foreach ($steps as $step) {
            echo "- Order: {$step->order}";
            if (isset($step->role)) echo ", Role: {$step->role}";
            if (isset($step->user_id)) echo ", User: {$step->user_id}";
            echo "\n";
        }
        echo "\n";
    } else {
        echo "Tidak ada approval flow untuk Lembur\n\n";
    }
} else if (!$hasApprovedAction) {
    echo "ℹ️  INFO: Tidak ada approval action 'Approved', status 'Pending' adalah benar\n";
} else {
    echo "✅ STATUS KONSISTEN: Approval dan status sesuai\n";
}

// 5. Solusi yang disarankan
echo "=== SOLUSI YANG DISARANKAN ===\n";

if ($hasApprovedAction && $statusIsPending) {
    echo "1. MANUAL FIX - Update status di database:\n";
    echo "   UPDATE process_approval_statuses \n";
    echo "   SET status = 'Approved', updated_at = NOW() \n";
    echo "   WHERE approvable_type = 'App\\\\Models\\\\Lembur' AND approvable_id = 5;\n\n";

    echo "2. INVESTIGATE - Cek log aplikasi untuk error saat approval\n";
    echo "3. TEST - Coba approval lembur baru untuk reproduksi bug\n";
    echo "4. FIX CODE - Perbaiki sistem approval jika ada bug\n\n";

    echo "Apakah ingin auto-fix status ini? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);

    if (strtolower($input) === 'y') {
        try {
            DB::table('process_approval_statuses')
                ->where('approvable_type', 'App\\Models\\Lembur')
                ->where('approvable_id', 5)
                ->update([
                    'status' => 'Approved',
                    'updated_at' => now()
                ]);

            echo "✅ Status berhasil diupdate ke 'Approved'\n";
            echo "Silakan test ulang payroll untuk melihat hasilnya\n";
        } catch (Exception $e) {
            echo "❌ Error updating status: {$e->getMessage()}\n";
        }
    }
}
