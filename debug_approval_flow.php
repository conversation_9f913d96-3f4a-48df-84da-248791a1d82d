<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== DEBUG APPROVAL FLOW CONFIGURATION ===\n\n";

// 1. Cek approval flows yang ada
echo "=== APPROVAL FLOWS ===\n";
$flows = DB::table('process_approval_flows')->get();

if ($flows->count() > 0) {
    foreach ($flows as $flow) {
        echo "ID: {$flow->id}\n";
        echo "Name: {$flow->name}\n";
        echo "Approvable Type: {$flow->approvable_type}\n";
        if (isset($flow->active)) {
            echo "Active: " . ($flow->active ? 'Yes' : 'No') . "\n";
        }
        echo "Created: {$flow->created_at}\n\n";
    }
} else {
    echo "❌ TIDAK ADA APPROVAL FLOWS!\n\n";
}

// 2. Cek approval flow steps
echo "=== APPROVAL FLOW STEPS ===\n";
foreach ($flows as $flow) {
    echo "--- Flow: {$flow->name} (ID: {$flow->id}) ---\n";
    
    $steps = DB::table('process_approval_flow_steps')
        ->where('process_approval_flow_id', $flow->id)
        ->orderBy('order')
        ->get();
    
    if ($steps->count() > 0) {
        foreach ($steps as $step) {
            echo "Step {$step->order}:\n";
            if (isset($step->role_id)) echo "  Role ID: {$step->role_id}\n";
            if (isset($step->user_id)) echo "  User ID: {$step->user_id}\n";
            if (isset($step->action)) echo "  Action: {$step->action}\n";
            echo "\n";
        }
    } else {
        echo "❌ TIDAK ADA STEPS untuk flow ini!\n\n";
    }
}

// 3. Cek roles yang ada
echo "=== ROLES CONFIGURATION ===\n";
$roles = DB::table('roles')->get();
foreach ($roles as $role) {
    echo "ID: {$role->id}, Name: {$role->name}\n";
}
echo "\n";

// 4. Cek users dengan roles
echo "=== USERS WITH ROLES ===\n";
$userRoles = DB::table('model_has_roles')
    ->join('users', 'model_has_roles.model_id', '=', 'users.id')
    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
    ->where('model_has_roles.model_type', 'App\\Models\\User')
    ->select('users.id as user_id', 'users.name', 'roles.id as role_id', 'roles.name as role_name')
    ->get();

$roleGroups = $userRoles->groupBy('role_name');
foreach ($roleGroups as $roleName => $users) {
    echo "Role: {$roleName}\n";
    foreach ($users as $user) {
        echo "  - {$user->name} (ID: {$user->user_id})\n";
    }
    echo "\n";
}

// 5. Analisis masalah approval flow
echo "=== ANALISIS MASALAH APPROVAL FLOW ===\n";

$lemburFlow = $flows->where('approvable_type', 'App\\Models\\Lembur')->first();

if (!$lemburFlow) {
    echo "🔥 MASALAH UTAMA: Tidak ada approval flow untuk Lembur!\n";
    echo "SOLUSI: Buat approval flow untuk model Lembur\n\n";
} else {
    echo "✅ Approval flow untuk Lembur EXISTS (ID: {$lemburFlow->id})\n";
    
    $lemburSteps = DB::table('process_approval_flow_steps')
        ->where('process_approval_flow_id', $lemburFlow->id)
        ->get();
    
    if ($lemburSteps->count() == 0) {
        echo "🔥 MASALAH: Approval flow tidak memiliki steps!\n";
        echo "SOLUSI: Tambahkan steps ke approval flow\n\n";
    } else {
        echo "✅ Approval flow memiliki {$lemburSteps->count()} steps\n";
        
        // Cek apakah ada users yang bisa approve
        $canApprove = false;
        foreach ($lemburSteps as $step) {
            if (isset($step->role_id)) {
                $usersWithRole = DB::table('model_has_roles')
                    ->where('role_id', $step->role_id)
                    ->where('model_type', 'App\\Models\\User')
                    ->count();
                
                if ($usersWithRole > 0) {
                    $canApprove = true;
                    break;
                }
            }
            
            if (isset($step->user_id)) {
                $userExists = DB::table('users')->where('id', $step->user_id)->exists();
                if ($userExists) {
                    $canApprove = true;
                    break;
                }
            }
        }
        
        if (!$canApprove) {
            echo "🔥 MASALAH: Tidak ada users yang bisa melakukan approval!\n";
            echo "SOLUSI: Pastikan ada users dengan role yang sesuai dengan approval steps\n\n";
        } else {
            echo "✅ Ada users yang bisa melakukan approval\n\n";
        }
    }
}

// 6. Cek mengapa status tidak terupdate setelah approval
echo "=== ANALISIS APPROVAL STATUS UPDATE ===\n";

// Cek lembur yang bermasalah
$problematicLembur = DB::select("
    SELECT 
        pas.approvable_id,
        pas.status as current_status,
        COUNT(pa.id) as total_approvals,
        SUM(CASE WHEN pa.approval_action = 'Approved' THEN 1 ELSE 0 END) as approved_count,
        MAX(pa.created_at) as last_approval_at
    FROM process_approval_statuses pas
    LEFT JOIN process_approvals pa ON pas.approvable_type = pa.approvable_type 
        AND pas.approvable_id = pa.approvable_id
    WHERE pas.approvable_type = 'App\\\\Models\\\\Lembur'
        AND pas.status = 'Pending'
    GROUP BY pas.approvable_id, pas.status
    HAVING approved_count > 0
    ORDER BY pas.approvable_id
");

if (count($problematicLembur) > 0) {
    echo "🐛 DITEMUKAN LEMBUR DENGAN STATUS TIDAK KONSISTEN:\n";
    foreach ($problematicLembur as $lembur) {
        echo "- Lembur ID {$lembur->approvable_id}: Status '{$lembur->current_status}' tapi ada {$lembur->approved_count} approval\n";
    }
    echo "\n";
    
    echo "KEMUNGKINAN PENYEBAB:\n";
    echo "1. Bug di package EightyNine\\Approvals\n";
    echo "2. Approval flow tidak complete dengan benar\n";
    echo "3. Error saat update status tapi approval record tetap tersimpan\n";
    echo "4. Multi-step approval yang belum selesai semua steps\n\n";
    
} else {
    echo "✅ Tidak ada lembur dengan status yang tidak konsisten\n\n";
}

// 7. Cek konfigurasi approval package
echo "=== APPROVAL PACKAGE CONFIGURATION ===\n";

$config = config('approvals');
if ($config) {
    echo "✅ Config approvals loaded\n";
    echo "Role Model: " . ($config['role_model'] ?? 'Not set') . "\n";
    echo "Prevent Self Approval: " . (($config['security']['prevent_self_approval'] ?? false) ? 'Yes' : 'No') . "\n";
    echo "Audit Approvals: " . (($config['security']['audit_approvals'] ?? false) ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ Config approvals tidak ditemukan\n";
}

$processApprovalConfig = config('process_approval');
if ($processApprovalConfig) {
    echo "✅ Config process_approval loaded\n";
    echo "Tenancy Enabled: " . (($processApprovalConfig['tenancy']['enabled'] ?? false) ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ Config process_approval tidak ditemukan\n";
}

echo "\n=== REKOMENDASI PERBAIKAN ===\n";
echo "1. Pastikan approval flow untuk Lembur dikonfigurasi dengan benar\n";
echo "2. Pastikan approval steps memiliki role/user yang valid\n";
echo "3. Cek log aplikasi saat melakukan approval untuk error\n";
echo "4. Pertimbangkan implementasi custom approval logic\n";
echo "5. Monitor approval status secara berkala\n";
echo "6. Test approval flow end-to-end\n";
