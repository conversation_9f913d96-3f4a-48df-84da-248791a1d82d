<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Lembur;
use Illuminate\Support\Facades\DB;

echo "=== FIX LEMBUR ID 7 APPROVAL STATUS ===\n\n";

// Ambil lembur ID 7
$lembur = Lembur::with(['approvalStatus', 'approvals'])->find(7);

if (!$lembur) {
    echo "Lembur ID 7 tidak ditemukan\n";
    exit;
}

echo "Lembur ID: {$lembur->id}\n";
echo "Tanggal: {$lembur->tanggal}\n";
echo "Jumlah Jam: {$lembur->jumlah_jam}\n\n";

// Cek approval status saat ini
echo "=== STATUS SAAT INI ===\n";
if ($lembur->approvalStatus) {
    echo "Status: {$lembur->approvalStatus->status}\n";
    echo "Updated At: {$lembur->approvalStatus->updated_at}\n\n";
}

// Cek approval records
echo "=== APPROVAL RECORDS ===\n";
$hasApprovedRecord = false;
if ($lembur->approvals && $lembur->approvals->count() > 0) {
    foreach ($lembur->approvals as $approval) {
        echo "- Action: {$approval->approval_action}\n";
        echo "  User ID: {$approval->user_id}\n";
        echo "  Created: {$approval->created_at}\n\n";
        
        if ($approval->approval_action === 'Approved') {
            $hasApprovedRecord = true;
        }
    }
}

// Analisis masalah
echo "=== ANALISIS ===\n";
echo "Ada approval record 'Approved': " . ($hasApprovedRecord ? 'YA' : 'TIDAK') . "\n";
echo "Status di approval_statuses: " . ($lembur->approvalStatus ? $lembur->approvalStatus->status : 'TIDAK ADA') . "\n";

if ($hasApprovedRecord && $lembur->approvalStatus && $lembur->approvalStatus->status === 'Pending') {
    echo "\n🐛 BUG DITEMUKAN: Status tidak konsisten!\n";
    echo "Ada approval 'Approved' tapi status masih 'Pending'\n\n";
    
    echo "Apakah ingin memperbaiki status ini? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($input) === 'y') {
        try {
            DB::beginTransaction();
            
            // Update status menjadi Approved
            $updated = DB::table('process_approval_statuses')
                ->where('approvable_type', 'App\\Models\\Lembur')
                ->where('approvable_id', 7)
                ->update([
                    'status' => 'Approved',
                    'updated_at' => now()
                ]);
            
            if ($updated) {
                echo "✅ Status berhasil diupdate ke 'Approved'\n";
                
                // Verifikasi perubahan
                $lembur->refresh();
                echo "Status baru: {$lembur->approvalStatus->status}\n";
                
                DB::commit();
                
                echo "\n🎉 Perbaikan berhasil!\n";
                echo "Lembur ID 7 sekarang akan masuk ke payroll\n";
                
            } else {
                echo "❌ Gagal mengupdate status\n";
                DB::rollback();
            }
            
        } catch (Exception $e) {
            DB::rollback();
            echo "❌ Error: {$e->getMessage()}\n";
        }
    } else {
        echo "Perbaikan dibatalkan\n";
    }
    
} else if (!$hasApprovedRecord) {
    echo "\n✅ STATUS BENAR: Belum ada approval 'Approved'\n";
    echo "Status 'Pending' adalah normal untuk lembur yang belum di-approve\n";
    
} else {
    echo "\n✅ STATUS KONSISTEN: Approval dan status sesuai\n";
}

echo "\n=== KESIMPULAN ===\n";
echo "Masalah yang sama dengan lembur sebelumnya:\n";
echo "- Approval record tersimpan dengan benar\n";
echo "- Tapi status di process_approval_statuses tidak terupdate\n";
echo "- Ini menunjukkan ada bug sistemik di approval package\n\n";

echo "REKOMENDASI:\n";
echo "1. Investigasi approval package EightyNine\\Approvals\n";
echo "2. Cek log aplikasi saat melakukan approval\n";
echo "3. Pertimbangkan implementasi custom approval system\n";
echo "4. Monitor approval status secara berkala\n";
