<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PayrollPeriod;
use App\Models\Karyawan;
use App\Models\User;
use App\Models\PenggajianKaryawan;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PayrollPeriodKaryawanSelectionTest extends TestCase
{
    use WithFaker;

    protected $user;
    protected $karyawan1;
    protected $karyawan2;
    protected $payrollPeriod;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create test karyawan with penggajian
        $this->karyawan1 = Karyawan::factory()->create([
            'status_aktif' => true,
            'nama_lengkap' => 'Test Karyawan 1'
        ]);

        $this->karyawan2 = Karyawan::factory()->create([
            'status_aktif' => true,
            'nama_lengkap' => 'Test Karyawan 2'
        ]);

        // Create penggajian for karyawan
        PenggajianKaryawan::factory()->create([
            'karyawan_id' => $this->karyawan1->id,
            'gaji_pokok' => 5000000
        ]);

        PenggajianKaryawan::factory()->create([
            'karyawan_id' => $this->karyawan2->id,
            'gaji_pokok' => 6000000
        ]);

        // Create payroll period
        $this->payrollPeriod = PayrollPeriod::factory()->create([
            'status' => 'draft',
            'karyawan_ids' => [$this->karyawan1->id, $this->karyawan2->id],
            'created_by' => $this->user->id
        ]);
    }

    public function test_payroll_period_can_store_selected_karyawan()
    {
        $this->assertNotNull($this->payrollPeriod->karyawan_ids);
        $this->assertCount(2, $this->payrollPeriod->karyawan_ids);
        $this->assertContains($this->karyawan1->id, $this->payrollPeriod->karyawan_ids);
        $this->assertContains($this->karyawan2->id, $this->payrollPeriod->karyawan_ids);
    }

    public function test_payroll_period_can_get_selected_karyawan()
    {
        $selectedKaryawan = $this->payrollPeriod->selectedKaryawan();
        
        $this->assertCount(2, $selectedKaryawan);
        $this->assertEquals('Test Karyawan 1', $selectedKaryawan->first()->nama_lengkap);
    }

    public function test_payroll_period_can_be_processed_only_with_selected_karyawan()
    {
        // Period with karyawan should be processable
        $this->assertTrue($this->payrollPeriod->canBeProcessed());

        // Period without karyawan should not be processable
        $emptyPeriod = PayrollPeriod::factory()->create([
            'status' => 'draft',
            'karyawan_ids' => null
        ]);
        
        $this->assertFalse($emptyPeriod->canBeProcessed());

        // Period with empty array should not be processable
        $emptyArrayPeriod = PayrollPeriod::factory()->create([
            'status' => 'draft',
            'karyawan_ids' => []
        ]);
        
        $this->assertFalse($emptyArrayPeriod->canBeProcessed());
    }

    public function test_payroll_period_jumlah_karyawan_terpilih_attribute()
    {
        $this->assertEquals(2, $this->payrollPeriod->jumlah_karyawan_terpilih);

        $emptyPeriod = PayrollPeriod::factory()->create([
            'karyawan_ids' => null
        ]);
        
        $this->assertEquals(0, $emptyPeriod->jumlah_karyawan_terpilih);
    }

    public function test_can_update_karyawan_selection()
    {
        // Update to select only one karyawan
        $this->payrollPeriod->update([
            'karyawan_ids' => [$this->karyawan1->id]
        ]);

        $this->payrollPeriod->refresh();
        
        $this->assertCount(1, $this->payrollPeriod->karyawan_ids);
        $this->assertContains($this->karyawan1->id, $this->payrollPeriod->karyawan_ids);
        $this->assertNotContains($this->karyawan2->id, $this->payrollPeriod->karyawan_ids);
    }
}
