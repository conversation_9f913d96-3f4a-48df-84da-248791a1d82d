<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PayrollPeriod;
use App\Models\Karyawan;
use App\Models\User;
use App\Models\Lembur;
use App\Models\KekuranganGaji;
use App\Models\Pesangon;
use App\Models\PenggajianKaryawan;
use App\Services\PayrollService;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class PayrollApprovedDataSimpleTest extends TestCase
{
    use WithFaker;

    public function test_lembur_filter_works_correctly()
    {
        // Ambil karyawan yang sudah ada
        $karyawan = Karyawan::where('status_aktif', true)
            ->whereHas('penggajian')
            ->first();

        if (!$karyawan) {
            $this->markTestSkipped('No active karyawan with penggajian found');
        }

        // Buat periode payroll
        $period = PayrollPeriod::where('status', 'draft')->first();
        if (!$period) {
            $this->markTestSkipped('No draft payroll period found');
        }

        // Test query lembur dengan filter approval
        $allLemburRecords = \App\Models\Lembur::where('karyawan_id', $karyawan->id)
            ->whereBetween('tanggal', [$period->tanggal_mulai, $period->tanggal_cutoff])
            ->with(['jenisLembur', 'approvalStatus'])
            ->get();

        // Filter hanya lembur yang sudah di-approve
        $approvedLemburRecords = $allLemburRecords->filter(function ($lembur) {
            // Jika tidak ada approval status, anggap sebagai data lama yang valid
            if (!$lembur->approvalStatus) {
                return true;
            }
            
            // Gunakan method isApprovalCompleted() dari ApprovableModel
            return $lembur->isApprovalCompleted() && 
                   $lembur->approvalStatus->status === 'Approved';
        });

        // Test bahwa filter berjalan tanpa error
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $approvedLemburRecords);
        
        // Jika ada lembur, pastikan yang difilter <= yang asli
        if ($allLemburRecords->count() > 0) {
            $this->assertLessThanOrEqual($allLemburRecords->count(), $approvedLemburRecords->count());
        }
    }

    public function test_kekurangan_gaji_query_works()
    {
        // Ambil karyawan yang sudah ada
        $karyawan = Karyawan::where('status_aktif', true)->first();
        
        if (!$karyawan) {
            $this->markTestSkipped('No active karyawan found');
        }

        // Buat periode payroll
        $period = PayrollPeriod::where('status', 'draft')->first();
        if (!$period) {
            $this->markTestSkipped('No draft payroll period found');
        }

        // Test query kekurangan gaji
        $kekuranganGaji = \App\Models\KekuranganGaji::byKaryawan($karyawan->id)
            ->byStatus('approved')
            ->where('periode_kekurangan', '<', $period->tanggal_mulai)
            ->whereNull('tanggal_pembayaran')
            ->sum('nominal_kekurangan');

        // Test bahwa query berjalan tanpa error
        $this->assertIsNumeric($kekuranganGaji);
        $this->assertGreaterThanOrEqual(0, $kekuranganGaji);
    }

    public function test_pesangon_query_works()
    {
        // Ambil karyawan yang sudah ada
        $karyawan = Karyawan::where('status_aktif', true)->first();
        
        if (!$karyawan) {
            $this->markTestSkipped('No active karyawan found');
        }

        // Buat periode payroll
        $period = PayrollPeriod::where('status', 'draft')->first();
        if (!$period) {
            $this->markTestSkipped('No draft payroll period found');
        }

        // Test query pesangon
        $pesangon = \App\Models\Pesangon::byKaryawan($karyawan->id)
            ->byStatus('approved')
            ->where('periode_pembayaran', '<=', $period->tanggal_mulai->format('Y-m-01'))
            ->whereNull('tanggal_pembayaran')
            ->sum('nominal_pesangon');

        // Test bahwa query berjalan tanpa error
        $this->assertIsNumeric($pesangon);
        $this->assertGreaterThanOrEqual(0, $pesangon);
    }

    public function test_payroll_service_calculate_methods_work()
    {
        // Ambil karyawan yang sudah ada
        $karyawan = Karyawan::where('status_aktif', true)
            ->whereHas('penggajian')
            ->first();

        if (!$karyawan) {
            $this->markTestSkipped('No active karyawan with penggajian found');
        }

        // Buat periode payroll
        $period = PayrollPeriod::where('status', 'draft')->first();
        if (!$period) {
            $this->markTestSkipped('No draft payroll period found');
        }

        $payrollService = new PayrollService();

        // Test bahwa method private dapat diakses melalui reflection untuk testing
        $reflection = new \ReflectionClass($payrollService);
        
        // Test calculateLemburData
        $lemburMethod = $reflection->getMethod('calculateLemburData');
        $lemburMethod->setAccessible(true);
        $lemburData = $lemburMethod->invoke($payrollService, $karyawan, $period);
        
        $this->assertIsArray($lemburData);
        $this->assertArrayHasKey('lembur_biasa', $lemburData);
        $this->assertArrayHasKey('lembur_tanggal_merah', $lemburData);
        $this->assertArrayHasKey('lembur_tambah_hk', $lemburData);

        // Test calculateKekuranganGajiData
        $kekuranganMethod = $reflection->getMethod('calculateKekuranganGajiData');
        $kekuranganMethod->setAccessible(true);
        $kekuranganData = $kekuranganMethod->invoke($payrollService, $karyawan, $period);
        
        $this->assertIsArray($kekuranganData);
        $this->assertArrayHasKey('kekurangan_gaji_bulan_sebelum', $kekuranganData);
        $this->assertArrayHasKey('detail_kekurangan', $kekuranganData);

        // Test calculatePesangonData
        $pesangonMethod = $reflection->getMethod('calculatePesangonData');
        $pesangonMethod->setAccessible(true);
        $pesangonData = $pesangonMethod->invoke($payrollService, $karyawan, $period);
        
        $this->assertIsArray($pesangonData);
        $this->assertArrayHasKey('pesangon', $pesangonData);
        $this->assertArrayHasKey('detail_pesangon', $pesangonData);
    }
}
