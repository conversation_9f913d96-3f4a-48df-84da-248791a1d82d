<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PayrollPeriod;
use App\Models\PayrollTransaction;
use App\Models\Karyawan;
use App\Models\User;
use App\Models\Lembur;
use App\Models\KekuranganGaji;
use App\Models\Pesangon;
use App\Models\PenggajianKaryawan;
use App\Services\PayrollService;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class PayrollApprovedDataTest extends TestCase
{
    use WithFaker;

    protected $user;
    protected $karyawan;
    protected $payrollPeriod;
    protected $payrollService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create test karyawan
        $this->karyawan = Karyawan::factory()->create([
            'status_aktif' => true,
            'nama_lengkap' => 'Test Karyawan'
        ]);

        // Create penggajian for karyawan
        PenggajianKaryawan::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'periode_gaji' => 'Basis Gaji',
            'gaji_pokok' => 5000000
        ]);

        // Create payroll period
        $this->payrollPeriod = PayrollPeriod::factory()->create([
            'status' => 'draft',
            'tanggal_mulai' => Carbon::now()->startOfMonth(),
            'tanggal_cutoff' => Carbon::now()->endOfMonth(),
            'karyawan_ids' => [$this->karyawan->id],
            'created_by' => $this->user->id
        ]);

        $this->payrollService = new PayrollService();
    }

    public function test_payroll_only_includes_approved_lembur()
    {
        // Create approved lembur
        $approvedLembur = Lembur::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal' => $this->payrollPeriod->tanggal_mulai->addDays(5),
            'jumlah_jam' => 2,
            'upah_lembur' => 100000
        ]);

        // Simulate approval (this would normally be done through the approval system)
        // For testing, we'll create the approval status manually
        $approvedLembur->approvalStatus()->create([
            'status' => 'Approved',
            'approved_by' => $this->user->id,
            'approved_at' => now()
        ]);

        // Create non-approved lembur
        $pendingLembur = Lembur::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'tanggal' => $this->payrollPeriod->tanggal_mulai->addDays(10),
            'jumlah_jam' => 3,
            'upah_lembur' => 150000
        ]);

        // Generate payroll using the public method
        $this->payrollService->generatePayroll($this->payrollPeriod, [$this->karyawan->id]);

        // Get the generated payroll transaction
        $payrollTransaction = PayrollTransaction::where('karyawan_id', $this->karyawan->id)
            ->where('payroll_period_id', $this->payrollPeriod->id)
            ->first();

        $this->assertNotNull($payrollTransaction);

        // Should include approved lembur but not pending lembur
        // The exact amount depends on the calculation logic, but it should be > 0 and < 150000
        $this->assertGreaterThan(0, $payrollTransaction->lembur_biasa + $payrollTransaction->lembur_tanggal_merah + $payrollTransaction->lembur_tambah_hk);
        $this->assertLessThan(150000, $payrollTransaction->lembur_biasa + $payrollTransaction->lembur_tanggal_merah + $payrollTransaction->lembur_tambah_hk);
    }

    public function test_payroll_includes_approved_kekurangan_gaji()
    {
        // Create approved kekurangan gaji
        KekuranganGaji::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'periode_kekurangan' => $this->payrollPeriod->tanggal_mulai->subMonth(),
            'nominal_kekurangan' => 500000,
            'status' => 'approved',
            'tanggal_pembayaran' => null // Belum dibayar
        ]);

        // Create pending kekurangan gaji (should not be included)
        KekuranganGaji::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'periode_kekurangan' => $this->payrollPeriod->tanggal_mulai->subMonth(),
            'nominal_kekurangan' => 300000,
            'status' => 'pending',
            'tanggal_pembayaran' => null
        ]);

        // Generate payroll
        $this->payrollService->generatePayrollForKaryawan($this->payrollPeriod, $this->karyawan);

        // Get the generated payroll transaction
        $payrollTransaction = PayrollTransaction::where('karyawan_id', $this->karyawan->id)
            ->where('payroll_period_id', $this->payrollPeriod->id)
            ->first();

        $this->assertNotNull($payrollTransaction);
        $this->assertEquals(500000, $payrollTransaction->kekurangan_gaji_bulan_sebelum);
    }

    public function test_payroll_includes_approved_pesangon()
    {
        // Create approved pesangon
        Pesangon::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'periode_pembayaran' => $this->payrollPeriod->tanggal_mulai,
            'nominal_pesangon' => 2000000,
            'status' => 'approved',
            'tanggal_pembayaran' => null // Belum dibayar
        ]);

        // Create pending pesangon (should not be included)
        Pesangon::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'periode_pembayaran' => $this->payrollPeriod->tanggal_mulai,
            'nominal_pesangon' => 1000000,
            'status' => 'pending',
            'tanggal_pembayaran' => null
        ]);

        // Generate payroll
        $this->payrollService->generatePayrollForKaryawan($this->payrollPeriod, $this->karyawan);

        // Get the generated payroll transaction
        $payrollTransaction = PayrollTransaction::where('karyawan_id', $this->karyawan->id)
            ->where('payroll_period_id', $this->payrollPeriod->id)
            ->first();

        $this->assertNotNull($payrollTransaction);
        $this->assertEquals(2000000, $payrollTransaction->pesangon);
    }

    public function test_approved_payroll_marks_kekurangan_gaji_as_paid()
    {
        // Create approved kekurangan gaji
        $kekuranganGaji = KekuranganGaji::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'periode_kekurangan' => $this->payrollPeriod->tanggal_mulai->subMonth(),
            'nominal_kekurangan' => 500000,
            'status' => 'approved',
            'tanggal_pembayaran' => null
        ]);

        // Generate payroll
        $this->payrollService->generatePayrollForKaryawan($this->payrollPeriod, $this->karyawan);

        // Get the generated payroll transaction
        $payrollTransaction = PayrollTransaction::where('karyawan_id', $this->karyawan->id)
            ->where('payroll_period_id', $this->payrollPeriod->id)
            ->first();

        // Approve the payroll
        $payrollTransaction->approve($this->user->id);

        // Check that kekurangan gaji is marked as paid
        $kekuranganGaji->refresh();
        $this->assertEquals('paid', $kekuranganGaji->status);
        $this->assertNotNull($kekuranganGaji->tanggal_pembayaran);
    }

    public function test_approved_payroll_marks_pesangon_as_paid()
    {
        // Create approved pesangon
        $pesangon = Pesangon::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'periode_pembayaran' => $this->payrollPeriod->tanggal_mulai,
            'nominal_pesangon' => 2000000,
            'status' => 'approved',
            'tanggal_pembayaran' => null
        ]);

        // Generate payroll
        $this->payrollService->generatePayrollForKaryawan($this->payrollPeriod, $this->karyawan);

        // Get the generated payroll transaction
        $payrollTransaction = PayrollTransaction::where('karyawan_id', $this->karyawan->id)
            ->where('payroll_period_id', $this->payrollPeriod->id)
            ->first();

        // Approve the payroll
        $payrollTransaction->approve($this->user->id);

        // Check that pesangon is marked as paid
        $pesangon->refresh();
        $this->assertEquals('paid', $pesangon->status);
        $this->assertNotNull($pesangon->tanggal_pembayaran);
    }
}
