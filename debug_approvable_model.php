<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Lembur;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

echo "=== DEBUG APPROVABLE MODEL APPROVE METHOD ===\n\n";

// Ambil lembur terbaru
$lembur = Lembur::with(['approvalStatus', 'approvals'])->orderBy('created_at', 'desc')->first();

if (!$lembur) {
    echo "Tidak ada lembur ditemukan\n";
    exit;
}

echo "Lembur ID: {$lembur->id}\n";
echo "Status saat ini: " . ($lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status') . "\n";
echo "isSubmitted(): " . ($lembur->isSubmitted() ? 'true' : 'false') . "\n";
echo "isApprovalCompleted(): " . ($lembur->isApprovalCompleted() ? 'true' : 'false') . "\n\n";

// Cek approval flow steps
echo "=== APPROVAL FLOW STEPS ===\n";
$flow = DB::table('process_approval_flows')
    ->where('approvable_type', 'App\\Models\\Lembur')
    ->first();

if ($flow) {
    $steps = DB::table('process_approval_flow_steps')
        ->where('process_approval_flow_id', $flow->id)
        ->orderBy('order')
        ->get();
    
    echo "Total steps: {$steps->count()}\n";
    foreach ($steps as $step) {
        echo "Step {$step->order}: Role ID {$step->role_id}, Action: {$step->action}\n";
    }
    echo "\n";
}

// Cek approval records yang ada
echo "=== EXISTING APPROVAL RECORDS ===\n";
if ($lembur->approvals && $lembur->approvals->count() > 0) {
    foreach ($lembur->approvals as $approval) {
        echo "- Action: {$approval->approval_action}, User: {$approval->user_id}, Created: {$approval->created_at}\n";
    }
} else {
    echo "Tidak ada approval records\n";
}
echo "\n";

// Simulasi approval process
echo "=== SIMULASI APPROVAL PROCESS ===\n";

// Login sebagai user yang bisa approve (manager_hrd)
$managerHrd = DB::table('model_has_roles')
    ->join('users', 'model_has_roles.model_id', '=', 'users.id')
    ->where('model_has_roles.role_id', 2) // manager_hrd
    ->where('model_has_roles.model_type', 'App\\Models\\User')
    ->select('users.*')
    ->first();

if (!$managerHrd) {
    echo "❌ Tidak ada user dengan role manager_hrd\n";
    exit;
}

echo "Approver: {$managerHrd->name} (ID: {$managerHrd->id})\n";

// Set auth user
Auth::loginUsingId($managerHrd->id);

// Cek apakah bisa di-approve
echo "canBeApprovedBy(): " . ($lembur->canBeApprovedBy(Auth::user()) ? 'true' : 'false') . "\n";

if (!$lembur->canBeApprovedBy(Auth::user())) {
    echo "❌ User tidak bisa approve lembur ini\n";
    
    // Cek alasan
    if (!$lembur->isSubmitted()) {
        echo "Alasan: Lembur belum di-submit\n";
    } elseif ($lembur->isApprovalCompleted()) {
        echo "Alasan: Approval sudah completed\n";
    } else {
        echo "Alasan: User tidak memiliki permission untuk approve\n";
    }
    
    exit;
}

echo "✅ User bisa approve lembur ini\n\n";

// Cek status sebelum approval
echo "=== STATUS SEBELUM APPROVAL ===\n";
echo "Approval Status: " . ($lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status') . "\n";
echo "Total approval records: " . ($lembur->approvals ? $lembur->approvals->count() : 0) . "\n\n";

// Lakukan approval
echo "=== MELAKUKAN APPROVAL ===\n";
try {
    DB::beginTransaction();
    
    // Panggil method approve dari ApprovableModel
    $result = $lembur->approve(comment: 'Test approval', user: Auth::user());
    
    echo "Approval method result: " . ($result ? 'true' : 'false') . "\n";
    
    // Refresh model untuk melihat perubahan
    $lembur->refresh();
    
    echo "\n=== STATUS SETELAH APPROVAL ===\n";
    echo "Approval Status: " . ($lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status') . "\n";
    echo "isApprovalCompleted(): " . ($lembur->isApprovalCompleted() ? 'true' : 'false') . "\n";
    echo "Total approval records: " . ($lembur->approvals ? $lembur->approvals->count() : 0) . "\n";
    
    // Cek approval records terbaru
    $latestApproval = $lembur->approvals()->latest()->first();
    if ($latestApproval) {
        echo "Latest approval action: {$latestApproval->approval_action}\n";
        echo "Latest approval user: {$latestApproval->user_id}\n";
        echo "Latest approval created: {$latestApproval->created_at}\n";
    }
    
    DB::commit();
    
    echo "\n✅ Approval berhasil!\n";
    
} catch (Exception $e) {
    DB::rollback();
    echo "❌ Error saat approval: {$e->getMessage()}\n";
    echo "Stack trace:\n{$e->getTraceAsString()}\n";
}

// Analisis hasil
echo "\n=== ANALISIS HASIL ===\n";

$finalStatus = $lembur->approvalStatus ? $lembur->approvalStatus->status : 'No status';
$hasApprovedRecord = $lembur->approvals()->where('approval_action', 'Approved')->exists();

echo "Final status: {$finalStatus}\n";
echo "Has approved record: " . ($hasApprovedRecord ? 'true' : 'false') . "\n";

if ($hasApprovedRecord && $finalStatus === 'Pending') {
    echo "\n🐛 BUG CONFIRMED: Approval record created but status not updated!\n";
    echo "Ini menunjukkan ada masalah di package EightyNine\\Approvals\n";
    echo "Package tidak mengupdate status approval_statuses setelah approval\n\n";
    
    echo "SOLUSI YANG DISARANKAN:\n";
    echo "1. Override method approve() di model Lembur\n";
    echo "2. Implementasi custom approval logic\n";
    echo "3. Update package ke versi terbaru\n";
    echo "4. Buat custom approval system\n";
    
} else if ($finalStatus === 'Approved') {
    echo "\n✅ APPROVAL BERHASIL: Status terupdate dengan benar\n";
} else {
    echo "\n⚠️  STATUS TIDAK JELAS: Perlu investigasi lebih lanjut\n";
}
