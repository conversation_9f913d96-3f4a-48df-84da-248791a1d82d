<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\PayrollTransaction;
use App\Models\Lembur;
use App\Models\PayrollPeriod;
use App\Models\Karyawan;

// Ambil PayrollTransaction ID 845
$payrollTransaction = PayrollTransaction::with(['karyawan', 'payrollPeriod'])->find(845);

if (!$payrollTransaction) {
    echo "PayrollTransaction ID 845 tidak ditemukan\n";
    exit;
}

echo "=== DEBUG LEMBUR PAYROLL TRANSACTION ID 845 ===\n\n";

echo "Karyawan: {$payrollTransaction->karyawan->nama_lengkap} (ID: {$payrollTransaction->karyawan_id})\n";
echo "Periode: {$payrollTransaction->payrollPeriod->nama_periode}\n";
echo "Tanggal Mulai: {$payrollTransaction->payrollPeriod->tanggal_mulai}\n";
echo "Tanggal Cutoff: {$payrollTransaction->payrollPeriod->tanggal_cutoff}\n";
echo "Lembur Biasa: Rp " . number_format($payrollTransaction->lembur_biasa, 0, ',', '.') . "\n";
echo "Lembur Tanggal Merah: Rp " . number_format($payrollTransaction->lembur_tanggal_merah, 0, ',', '.') . "\n";
echo "Lembur Tambah HK: Rp " . number_format($payrollTransaction->lembur_tambah_hk, 0, ',', '.') . "\n\n";

// Cek data lembur untuk karyawan dalam periode
echo "=== DATA LEMBUR DALAM PERIODE ===\n";

$allLemburRecords = Lembur::where('karyawan_id', $payrollTransaction->karyawan_id)
    ->whereBetween('tanggal', [
        $payrollTransaction->payrollPeriod->tanggal_mulai,
        $payrollTransaction->payrollPeriod->tanggal_cutoff
    ])
    ->with(['jenisLembur', 'approvalStatus', 'approvals'])
    ->get();

echo "Total lembur dalam periode: {$allLemburRecords->count()}\n\n";

if ($allLemburRecords->count() > 0) {
    foreach ($allLemburRecords as $lembur) {
        echo "--- Lembur ID: {$lembur->id} ---\n";
        echo "Tanggal: {$lembur->tanggal}\n";
        echo "Jumlah Jam: {$lembur->jumlah_jam}\n";
        echo "Jenis Lembur: " . ($lembur->jenisLembur ? $lembur->jenisLembur->nama_jenis : 'Tidak ada') . "\n";
        echo "Upah Lembur: Rp " . number_format($lembur->upah_lembur, 0, ',', '.') . "\n";

        // Cek approval status
        if ($lembur->approvalStatus) {
            echo "Approval Status: {$lembur->approvalStatus->status}\n";
            echo "Is Approval Completed: " . ($lembur->isApprovalCompleted() ? 'Yes' : 'No') . "\n";
            echo "Creator ID: {$lembur->approvalStatus->creator_id}\n";

            // Cek apakah lembur ini akan difilter
            $isApproved = $lembur->isApprovalCompleted() && $lembur->approvalStatus->status === 'Approved';
            echo "Will be included in payroll: " . ($isApproved ? 'YES' : 'NO') . "\n";
        } else {
            echo "Approval Status: Tidak ada (data lama - akan dimasukkan)\n";
            echo "Will be included in payroll: YES (legacy data)\n";
        }

        // Hitung upah lembur
        try {
            $upahHitung = $lembur->hitungUpahLembur();
            echo "Calculated Upah: Rp " . number_format($upahHitung, 0, ',', '.') . "\n";
        } catch (Exception $e) {
            echo "Error calculating upah: {$e->getMessage()}\n";
        }

        echo "\n";
    }

    // Filter lembur yang akan dimasukkan ke payroll
    echo "=== FILTER LEMBUR UNTUK PAYROLL ===\n";

    $approvedLemburRecords = $allLemburRecords->filter(function ($lembur) {
        // Jika tidak ada approval status, anggap sebagai data lama yang valid
        if (!$lembur->approvalStatus) {
            return true;
        }

        // Cek apakah approval sudah completed dan ada approval record dengan action 'Approved'
        if ($lembur->isApprovalCompleted()) {
            // Cek approval records untuk memastikan ada yang approved
            $hasApprovedRecord = $lembur->approvals()
                ->where('approval_action', 'Approved')
                ->exists();

            return $hasApprovedRecord;
        }

        // Fallback: cek status approval_status
        return $lembur->approvalStatus->status === 'Approved';
    });

    echo "Lembur yang akan dimasukkan ke payroll: {$approvedLemburRecords->count()}\n\n";

    if ($approvedLemburRecords->count() > 0) {
        $totalUpah = 0;
        foreach ($approvedLemburRecords as $lembur) {
            $upah = $lembur->hitungUpahLembur();
            $totalUpah += $upah;
            echo "- Lembur ID {$lembur->id}: Rp " . number_format($upah, 0, ',', '.') . "\n";
        }
        echo "Total Upah Lembur: Rp " . number_format($totalUpah, 0, ',', '.') . "\n";
    } else {
        echo "Tidak ada lembur yang memenuhi kriteria untuk dimasukkan ke payroll\n";
    }
} else {
    echo "Tidak ada data lembur dalam periode ini\n";
}

echo "\n=== KESIMPULAN ===\n";
if ($allLemburRecords->count() > 0) {
    // Gunakan logic yang sama dengan PayrollService
    $approvedCount = $allLemburRecords->filter(function ($lembur) {
        if (!$lembur->approvalStatus) return true;

        if ($lembur->isApprovalCompleted()) {
            $hasApprovedRecord = $lembur->approvals()
                ->where('approval_action', 'Approved')
                ->exists();
            return $hasApprovedRecord;
        }

        return $lembur->approvalStatus->status === 'Approved';
    })->count();

    if ($approvedCount == 0) {
        echo "MASALAH: Ada {$allLemburRecords->count()} data lembur tapi tidak ada yang approved\n";
        echo "SOLUSI: Approve lembur melalui sistem approval atau cek status approval\n";
    } else {
        echo "✅ BERHASIL: Ada {$approvedCount} lembur yang approved dari {$allLemburRecords->count()} total lembur\n";
        echo "Lembur sudah akan masuk ke payroll dengan total Rp " . number_format($approvedLemburRecords->sum(function ($l) {
            return $l->hitungUpahLembur();
        }), 0, ',', '.') . "\n";
        echo "Jika komponen lembur di PayrollTransaction masih 0, perlu regenerate payroll\n";
    }
} else {
    echo "Tidak ada data lembur dalam periode payroll\n";
}
