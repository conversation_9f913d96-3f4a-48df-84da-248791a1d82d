<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== FIX ALL APPROVAL STATUS BUGS ===\n\n";

// Cari semua approval status yang bermasalah
$buggedApprovals = DB::select("
    SELECT 
        pas.id as status_id,
        pas.approvable_type,
        pas.approvable_id,
        pas.status as current_status,
        pas.updated_at as status_updated_at,
        COUNT(pa.id) as total_approvals,
        SUM(CASE WHEN pa.approval_action = 'Approved' THEN 1 ELSE 0 END) as approved_count,
        MAX(CASE WHEN pa.approval_action = 'Approved' THEN pa.created_at END) as last_approved_at
    FROM process_approval_statuses pas
    LEFT JOIN process_approvals pa ON pas.approvable_type = pa.approvable_type 
        AND pas.approvable_id = pa.approvable_id
    WHERE pas.status = 'Pending'
    GROUP BY pas.id, pas.approvable_type, pas.approvable_id, pas.status, pas.updated_at
    HAVING approved_count > 0
    ORDER BY pas.approvable_type, pas.approvable_id
");

echo "Ditemukan " . count($buggedApprovals) . " approval status yang bermasalah:\n\n";

if (count($buggedApprovals) == 0) {
    echo "✅ Tidak ada approval status yang bermasalah!\n";
    exit;
}

foreach ($buggedApprovals as $bug) {
    echo "--- {$bug->approvable_type} ID {$bug->approvable_id} ---\n";
    echo "Status ID: {$bug->status_id}\n";
    echo "Current Status: {$bug->current_status}\n";
    echo "Total Approvals: {$bug->total_approvals}\n";
    echo "Approved Count: {$bug->approved_count}\n";
    echo "Last Approved: {$bug->last_approved_at}\n";
    echo "Status Updated: {$bug->status_updated_at}\n\n";
}

echo "Apakah ingin memperbaiki semua status yang bermasalah? (y/n): ";
$handle = fopen("php://stdin", "r");
$input = trim(fgets($handle));
fclose($handle);

if (strtolower($input) !== 'y') {
    echo "Operasi dibatalkan\n";
    exit;
}

echo "\n🔧 Memulai perbaikan...\n\n";

$fixedCount = 0;
$errorCount = 0;

foreach ($buggedApprovals as $bug) {
    try {
        DB::beginTransaction();
        
        // Update status menjadi Approved
        $updated = DB::table('process_approval_statuses')
            ->where('id', $bug->status_id)
            ->update([
                'status' => 'Approved',
                'updated_at' => now()
            ]);
        
        if ($updated) {
            echo "✅ Fixed: {$bug->approvable_type} ID {$bug->approvable_id}\n";
            $fixedCount++;
        } else {
            echo "⚠️  Warning: No rows updated for {$bug->approvable_type} ID {$bug->approvable_id}\n";
        }
        
        DB::commit();
        
    } catch (Exception $e) {
        DB::rollback();
        echo "❌ Error fixing {$bug->approvable_type} ID {$bug->approvable_id}: {$e->getMessage()}\n";
        $errorCount++;
    }
}

echo "\n=== HASIL PERBAIKAN ===\n";
echo "Total yang diperbaiki: {$fixedCount}\n";
echo "Total error: {$errorCount}\n";

if ($fixedCount > 0) {
    echo "\n✅ Perbaikan selesai!\n";
    echo "Semua approval status yang bermasalah sudah diperbaiki.\n";
    echo "Sekarang lembur yang sudah di-approve akan masuk ke payroll.\n\n";
    
    echo "LANGKAH SELANJUTNYA:\n";
    echo "1. Test payroll baru untuk memastikan lembur masuk\n";
    echo "2. Regenerate payroll yang sudah ada jika diperlukan\n";
    echo "3. Investigasi penyebab bug untuk mencegah terulang\n";
} else {
    echo "\n⚠️  Tidak ada yang diperbaiki\n";
}

// Cek apakah masih ada yang bermasalah
$remainingBugs = DB::select("
    SELECT COUNT(*) as count
    FROM process_approval_statuses pas
    LEFT JOIN process_approvals pa ON pas.approvable_type = pa.approvable_type 
        AND pas.approvable_id = pa.approvable_id
    WHERE pas.status = 'Pending'
    GROUP BY pas.id, pas.approvable_type, pas.approvable_id, pas.status
    HAVING SUM(CASE WHEN pa.approval_action = 'Approved' THEN 1 ELSE 0 END) > 0
");

if (count($remainingBugs) == 0) {
    echo "\n🎉 Semua approval status sudah konsisten!\n";
} else {
    echo "\n⚠️  Masih ada " . count($remainingBugs) . " approval status yang bermasalah\n";
    echo "Mungkin perlu investigasi lebih lanjut\n";
}
